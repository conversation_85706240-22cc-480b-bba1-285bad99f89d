import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface AnalyticsEvent {
  event: string
  category: string
  action: string
  label?: string
  value?: number
  custom_parameters?: Record<string, any>
}

export interface UserSession {
  session_id: string
  user_id?: string
  start_time: number
  page_views: number
  events: AnalyticsEvent[]
  device_info: {
    user_agent: string
    screen_resolution: string
    language: string
    timezone: string
  }
}

export const useAnalyticsStore = defineStore('analytics', () => {
  const isInitialized = ref(false)
  const sessionId = ref<string>('')
  const userId = ref<string | null>(null)
  const currentSession = ref<UserSession | null>(null)
  
  const GA_MEASUREMENT_ID = import.meta.env.VITE_GA_MEASUREMENT_ID || ''
  const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'https://posterpos-backend.netlify.app'

  // Initialize analytics
  const initialize = async () => {
    if (isInitialized.value) return

    try {
      // Initialize Google Analytics 4
      if (GA_MEASUREMENT_ID) {
        await initializeGA4()
      }

      // Initialize custom analytics
      initializeCustomAnalytics()
      
      isInitialized.value = true
      console.log('Analytics initialized successfully')
    } catch (error) {
      console.error('Analytics initialization failed:', error)
    }
  }

  // Initialize Google Analytics 4
  const initializeGA4 = async () => {
    return new Promise<void>((resolve) => {
      // Load gtag script
      const script = document.createElement('script')
      script.async = true
      script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`
      document.head.appendChild(script)

      script.onload = () => {
        // Initialize gtag
        window.dataLayer = window.dataLayer || []
        function gtag(...args: any[]) {
          window.dataLayer.push(args)
        }
        window.gtag = gtag

        gtag('js', new Date())
        gtag('config', GA_MEASUREMENT_ID, {
          page_title: document.title,
          page_location: window.location.href,
          custom_map: {
            custom_parameter_1: 'user_type',
            custom_parameter_2: 'session_duration'
          }
        })

        resolve()
      }
    })
  }

  // Initialize custom analytics session
  const initializeCustomAnalytics = () => {
    sessionId.value = generateSessionId()
    
    currentSession.value = {
      session_id: sessionId.value,
      user_id: userId.value,
      start_time: Date.now(),
      page_views: 0,
      events: [],
      device_info: {
        user_agent: navigator.userAgent,
        screen_resolution: `${screen.width}x${screen.height}`,
        language: navigator.language,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      }
    }

    // Track session start
    trackEvent({
      event: 'session_start',
      category: 'engagement',
      action: 'session_start',
      custom_parameters: {
        session_id: sessionId.value,
        device_info: currentSession.value.device_info
      }
    })
  }

  // Generate unique session ID
  const generateSessionId = (): string => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Track page view
  const trackPageView = (page: string, title?: string) => {
    if (!isInitialized.value) return

    // Google Analytics
    if (window.gtag && GA_MEASUREMENT_ID) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_title: title || document.title,
        page_location: window.location.href
      })
    }

    // Custom analytics
    if (currentSession.value) {
      currentSession.value.page_views++
    }

    trackEvent({
      event: 'page_view',
      category: 'engagement',
      action: 'page_view',
      label: page,
      custom_parameters: {
        page_title: title || document.title,
        page_location: window.location.href
      }
    })
  }

  // Track custom event
  const trackEvent = (eventData: AnalyticsEvent) => {
    if (!isInitialized.value) return

    try {
      // Google Analytics
      if (window.gtag && GA_MEASUREMENT_ID) {
        window.gtag('event', eventData.action, {
          event_category: eventData.category,
          event_label: eventData.label,
          value: eventData.value,
          custom_parameters: eventData.custom_parameters
        })
      }

      // Custom analytics - store in session
      if (currentSession.value) {
        currentSession.value.events.push({
          ...eventData,
          custom_parameters: {
            ...eventData.custom_parameters,
            timestamp: Date.now(),
            session_id: sessionId.value
          }
        })
      }

      // Send to backend
      sendEventToBackend(eventData)
    } catch (error) {
      console.error('Error tracking event:', error)
    }
  }

  // E-commerce tracking
  const trackPurchase = (transactionData: {
    transaction_id: string
    value: number
    currency: string
    items: Array<{
      item_id: string
      item_name: string
      category: string
      quantity: number
      price: number
    }>
  }) => {
    // Google Analytics Enhanced E-commerce
    if (window.gtag && GA_MEASUREMENT_ID) {
      window.gtag('event', 'purchase', {
        transaction_id: transactionData.transaction_id,
        value: transactionData.value,
        currency: transactionData.currency,
        items: transactionData.items
      })
    }

    // Custom tracking
    trackEvent({
      event: 'purchase',
      category: 'ecommerce',
      action: 'purchase',
      value: transactionData.value,
      custom_parameters: transactionData
    })
  }

  // Track add to cart
  const trackAddToCart = (item: {
    item_id: string
    item_name: string
    category: string
    quantity: number
    price: number
  }) => {
    if (window.gtag && GA_MEASUREMENT_ID) {
      window.gtag('event', 'add_to_cart', {
        currency: 'UAH',
        value: item.price * item.quantity,
        items: [item]
      })
    }

    trackEvent({
      event: 'add_to_cart',
      category: 'ecommerce',
      action: 'add_to_cart',
      label: item.item_name,
      value: item.price * item.quantity,
      custom_parameters: item
    })
  }

  // Track admin actions
  const trackAdminAction = (action: string, details?: Record<string, any>) => {
    trackEvent({
      event: 'admin_action',
      category: 'admin',
      action: action,
      custom_parameters: {
        ...details,
        admin_user: userId.value,
        timestamp: Date.now()
      }
    })
  }

  // Track PWA events
  const trackPWAEvent = (event: 'install_prompt' | 'install_accepted' | 'install_dismissed' | 'offline_usage') => {
    trackEvent({
      event: 'pwa_event',
      category: 'pwa',
      action: event,
      custom_parameters: {
        standalone: window.matchMedia('(display-mode: standalone)').matches,
        online: navigator.onLine
      }
    })
  }

  // Track user engagement
  const trackEngagement = (action: string, duration?: number) => {
    trackEvent({
      event: 'user_engagement',
      category: 'engagement',
      action: action,
      value: duration,
      custom_parameters: {
        session_duration: currentSession.value ? Date.now() - currentSession.value.start_time : 0
      }
    })
  }

  // Send event to backend
  const sendEventToBackend = async (eventData: AnalyticsEvent) => {
    try {
      await fetch(`${API_BASE_URL}/api/analytics/event`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...eventData,
          session_id: sessionId.value,
          user_id: userId.value,
          timestamp: Date.now(),
          url: window.location.href,
          referrer: document.referrer
        })
      })
    } catch (error) {
      // Silently fail - analytics shouldn't break the app
      console.debug('Analytics backend error:', error)
    }
  }

  // Send session data to backend
  const sendSessionToBackend = async () => {
    if (!currentSession.value) return

    try {
      await fetch(`${API_BASE_URL}/api/analytics/session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...currentSession.value,
          end_time: Date.now(),
          duration: Date.now() - currentSession.value.start_time
        })
      })
    } catch (error) {
      console.debug('Session analytics error:', error)
    }
  }

  // Set user ID
  const setUserId = (id: string) => {
    userId.value = id
    
    if (window.gtag && GA_MEASUREMENT_ID) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        user_id: id
      })
    }

    if (currentSession.value) {
      currentSession.value.user_id = id
    }
  }

  // End session
  const endSession = async () => {
    if (currentSession.value) {
      trackEvent({
        event: 'session_end',
        category: 'engagement',
        action: 'session_end',
        value: Date.now() - currentSession.value.start_time,
        custom_parameters: {
          page_views: currentSession.value.page_views,
          events_count: currentSession.value.events.length
        }
      })

      await sendSessionToBackend()
    }
  }

  return {
    // State
    isInitialized,
    sessionId,
    userId,
    currentSession,

    // Actions
    initialize,
    trackPageView,
    trackEvent,
    trackPurchase,
    trackAddToCart,
    trackAdminAction,
    trackPWAEvent,
    trackEngagement,
    setUserId,
    endSession
  }
})

// Global type declarations
declare global {
  interface Window {
    dataLayer: any[]
    gtag: (...args: any[]) => void
  }
}
