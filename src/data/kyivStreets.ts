// Comprehensive database of Kyiv streets with house numbers for offline autocomplete
// This provides fallback when external APIs are unavailable

export interface KyivAddress {
  street: string
  houseNumbers: string[]
  district?: string
  type: 'street' | 'boulevard' | 'avenue' | 'square' | 'metro'
}

export const kyivAddresses: KyivAddress[] = [
  // Central Kyiv - Shevchenkivskyi District
  {
    street: 'вул. Хрещатик',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50'],
    district: 'Шевченківський район',
    type: 'street'
  },
  {
    street: 'вул. Володимирська',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80'],
    district: 'Шевченківський район',
    type: 'street'
  },
  {
    street: 'вул. Інститутська',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40'],
    district: 'Шевченківський район',
    type: 'street'
  },
  {
    street: 'вул. Банкова',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20'],
    district: 'Шевченківський район',
    type: 'street'
  },
  {
    street: 'вул. Грушевського',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30'],
    district: 'Шевченківський район',
    type: 'street'
  },

  // Pechersk District
  {
    street: 'вул. Саксаганського',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100'],
    district: 'Печерський район',
    type: 'street'
  },
  {
    street: 'вул. Велика Васильківська',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100', '101', '102', '103', '104', '105', '106', '107', '108', '109', '110', '111', '112', '113', '114', '115', '116', '117', '118', '119', '120'],
    district: 'Печерський район',
    type: 'street'
  },
  {
    street: 'вул. Антоновича',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100'],
    district: 'Печерський район',
    type: 'street'
  },
  {
    street: 'бул. Лесі Українки',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50'],
    district: 'Печерський район',
    type: 'boulevard'
  },

  // Solomianskyi District
  {
    street: 'просп. Перемоги',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100', '101', '102', '103', '104', '105', '106', '107', '108', '109', '110', '111', '112', '113', '114', '115', '116', '117', '118', '119', '120'],
    district: 'Солом\'янський район',
    type: 'avenue'
  },
  {
    street: 'просп. Науки',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80'],
    district: 'Голосіївський район',
    type: 'avenue'
  },
  {
    street: 'просп. Оболонський',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50'],
    district: 'Оболонський район',
    type: 'avenue'
  },
  {
    street: 'вул. Левка Лук\'яненка',
    houseNumbers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40'],
    district: 'Оболонський район',
    type: 'street'
  },
  'вул. Відрадний просп.',
  'вул. Повітрофлотський просп.',
  'вул. Василя Липківського',
  'вул. Борщагівська',
  'вул. Кадетський Гай',
  'вул. Ушинського',
  'вул. Гарматна',
  'вул. Політехнічна',
  'вул. Фрунзе',
  'вул. Вадима Гетьмана',
  'вул. Ломоносова',
  'вул. Академіка Заболотного',
  'вул. Деміївська',
  'вул. Столичне шосе',
  'вул. Кільцева дорога',

  // Holosiivskyi District
  'просп. Науки',
  'просп. Академіка Глушкова',
  'вул. Васильківська',
  'вул. Теремківська',
  'вул. Феодосійська',
  'вул. Кіото',
  'вул. Ломоносова',
  'вул. Заболотного',
  'вул. Горького',
  'вул. Жмеринська',
  'вул. Жуляни',
  'вул. Авіаконструктора Антонова',
  'вул. Метрологічна',
  'вул. Новгородська',
  'вул. Пирогова',
  'вул. Медвідчука',

  // Shevchenkivskyi District (continued)
  'вул. Дмитрівська',
  'вул. Пушкінська',
  'вул. Богдана Хмельницького',
  'вул. Льва Толстого',
  'вул. Прорізна',
  'вул. Городецького',
  'вул. Жилянська',
  'вул. Мельникова',
  'вул. Тарасівська',
  'вул. Рогнідинська',
  'вул. Ярославів Вал',
  'вул. Золотоворітська',
  'вул. Лютеранська',
  'вул. Стрілецька',
  'вул. Олеся Гончара',
  'вул. Артема',
  'вул. Шота Руставелі',
  'вул. Терещенківська',
  'вул. Володимирський узвіз',

  // Podilskyi District
  'вул. Сагайдачного',
  'вул. Хорива',
  'вул. Межигірська',
  'вул. Набережно-Хрещатицька',
  'вул. Набережно-Луговська',
  'вул. Набережно-Рибальська',
  'вул. Спаська',
  'вул. Кирилівська',
  'вул. Фрунзе',
  'вул. Оболонська',
  'вул. Почайнинська',
  'вул. Верхньоюркова',
  'вул. Нижньоюркова',
  'вул. Боровиковського',
  'вул. Вознесенський узвіз',
  'вул. Костьольна',
  'вул. Братська',
  'вул. Флорівська',

  // Obolon District
  'просп. Оболонський',
  'просп. Героїв Сталінграда',
  'вул. Тимошенка',
  'вул. Богатирська',
  'вул. Малиновського',
  'вул. Озерна',
  'вул. Північна',
  'вул. Приозерна',
  'вул. Оболонська набережна',
  'вул. Автозаводська',
  'вул. Вербова',
  'вул. Іванова',
  'вул. Лайоша Гавро',
  'вул. Маршала Тимошенка',

  // Darnytskyi District
  'просп. Бажана',
  'вул. Ревуцького',
  'вул. Харківське шосе',
  'вул. Бориспільська',
  'вул. Драгоманова',
  'вул. Ахматової',
  'вул. Срібнокільська',
  'вул. Здолбунівська',
  'вул. Кошиця',
  'вул. Чавдар',
  'вул. Бальзака',
  'вул. Гмирі',
  'вул. Княжий Затон',
  'вул. Дніпровська набережна',

  // Desnianskyi District
  'просп. Маяковського',
  'вул. Лісна',
  'вул. Милославська',
  'вул. Закревського',
  'вул. Радунська',
  'вул. Братиславська',
  'вул. Червоноткацька',
  'вул. Бальзака',
  'вул. Гната Юри',
  'вул. Райдужна',
  'вул. Академіка Вільямса',

  // Dniprovskyi District
  'просп. Лобановського',
  'вул. Березняківська',
  'вул. Броварський просп.',
  'вул. Миру',
  'вул. Алма-Атинська',
  'вул. Райдужна',
  'вул. Курнатовського',
  'вул. Попудренка',
  'вул. Регенераторна',
  'вул. Електриків',
  'вул. Будівельників',

  // Sviatoshynskyi District
  'вул. Жукова',
  'вул. Кольцова',
  'вул. Якуба Коласа',
  'вул. Зодчих',
  'вул. Туполєва',
  'вул. Академіка Корольова',
  'вул. Кольцова',
  'вул. Симиренка',
  'вул. Стеценка',

  // Boulevards and Squares
  'бул. Лесі Українки',
  'бул. Тараса Шевченка',
  'бул. Дружби Народів',
  'бул. Верховної Ради',
  'пл. Незалежності',
  'пл. Софійська',
  'пл. Контрактова',
  'пл. Льва Толстого',
  'пл. Перемоги',
  'пл. Європейська',
  'пл. Поштова',
  'пл. Михайлівська',

  // Parks and Embankments
  'Хрещатий парк',
  'Маріїнський парк',
  'Володимирська гірка',
  'Парк Шевченка',
  'Гідропарк',
  'Парк Перемоги',
  'Голосіївський парк',
  'Дніпровська набережна',
  'Оболонська набережна',
  'Печерська набережна'
]

// Districts for additional context
export const kyivDistricts = [
  'Голосіївський район',
  'Дарницький район',
  'Деснянський район',
  'Дніпровський район',
  'Оболонський район',
  'Печерський район',
  'Подільський район',
  'Святошинський район',
  'Солом\'янський район',
  'Шевченківський район'
]

// Metro stations for reference
export const kyivMetroStations = [
  'м. Академмістечко',
  'м. Житомирська',
  'м. Святошин',
  'м. Нивки',
  'м. Берестейська',
  'м. Шулявська',
  'м. Політехнічний інститут',
  'м. Вокзальна',
  'м. Університет',
  'м. Театральна',
  'м. Хрещатик',
  'м. Арсенальна',
  'м. Дніпро',
  'м. Гідропарк',
  'м. Лівобережна',
  'м. Дарниця',
  'м. Чернігівська',
  'м. Лісова',
  'м. Героїв Дніпра',
  'м. Мінська',
  'м. Оболонь',
  'м. Почайна',
  'м. Тараса Шевченка',
  'м. Контрактова площа',
  'м. Поштова площа',
  'м. Майдан Незалежності',
  'м. Площа Льва Толстого',
  'м. Олімпійська',
  'м. Палац Україна',
  'м. Либідська',
  'м. Деміївська',
  'м. Голосіївська',
  'м. Васильківська',
  'м. Виставковий центр',
  'м. Іподром',
  'м. Теремки'
]

// Backward compatibility - simple street names
export const kyivStreets = kyivAddresses.map(addr =>
  typeof addr === 'string' ? addr : addr.street
).concat([
  // Add remaining simple streets
  'вул. Солом\'янська',
  'вул. Відрадний просп.',
  'вул. Повітрофлотський просп.',
  'вул. Василя Липківського',
  'вул. Борщагівська',
  'вул. Кадетський Гай',
  'вул. Ушинського',
  'вул. Гарматна',
  'вул. Політехнічна',
  'вул. Фрунзе',
  'вул. Вадима Гетьмана',
  'вул. Ломоносова',
  'вул. Академіка Заболотного',
  'вул. Деміївська',
  'вул. Столичне шосе',
  'вул. Кільцева дорога',
  'вул. Васильківська',
  'вул. Теремківська',
  'вул. Феодосійська',
  'вул. Кіото',
  'вул. Горького',
  'вул. Жмеринська',
  'вул. Жуляни',
  'вул. Авіаконструктора Антонова',
  'вул. Метрологічна',
  'вул. Новгородська',
  'вул. Пирогова',
  'вул. Медвідчука'
])

// Helper function to generate house numbers
export function generateHouseNumbers(max: number = 100): string[] {
  const numbers: string[] = []
  for (let i = 1; i <= max; i++) {
    numbers.push(i.toString())
    // Add some common letter suffixes
    if (i <= 20) {
      numbers.push(`${i}А`, `${i}Б`)
    }
  }
  return numbers
}

// Helper function to search addresses with house numbers
export function searchKyivAddresses(query: string): Array<{
  street: string
  houseNumber?: string
  fullAddress: string
  district?: string
  type?: string
}> {
  const normalizedQuery = query.toLowerCase().trim()
  const results: Array<{
    street: string
    houseNumber?: string
    fullAddress: string
    district?: string
    type?: string
  }> = []

  kyivAddresses.forEach(addr => {
    if (typeof addr === 'string') {
      if (addr.toLowerCase().includes(normalizedQuery)) {
        results.push({
          street: addr,
          fullAddress: `${addr}, Київ, Україна`,
          type: 'street'
        })
      }
    } else {
      const streetMatch = addr.street.toLowerCase().includes(normalizedQuery)

      if (streetMatch) {
        // If query contains numbers, try to match house numbers
        const numberMatch = normalizedQuery.match(/\d+[а-яё]?/i)

        if (numberMatch) {
          const queryNumber = numberMatch[0]
          const matchingHouses = addr.houseNumbers.filter(house =>
            house.toLowerCase().includes(queryNumber.toLowerCase())
          )

          if (matchingHouses.length > 0) {
            matchingHouses.slice(0, 5).forEach(house => {
              results.push({
                street: addr.street,
                houseNumber: house,
                fullAddress: `${addr.street}, ${house}, Київ, Україна`,
                district: addr.district,
                type: addr.type
              })
            })
          } else {
            // No house number match, show street only
            results.push({
              street: addr.street,
              fullAddress: `${addr.street}, Київ, Україна`,
              district: addr.district,
              type: addr.type
            })
          }
        } else {
          // No number in query, show street with some house numbers
          results.push({
            street: addr.street,
            fullAddress: `${addr.street}, Київ, Україна`,
            district: addr.district,
            type: addr.type
          })

          // Add a few house number suggestions
          addr.houseNumbers.slice(0, 3).forEach(house => {
            results.push({
              street: addr.street,
              houseNumber: house,
              fullAddress: `${addr.street}, ${house}, Київ, Україна`,
              district: addr.district,
              type: addr.type
            })
          })
        }
      }
    }
  })

  return results.slice(0, 10) // Limit results
}
