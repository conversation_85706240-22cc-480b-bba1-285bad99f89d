import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { Capacitor } from '@capacitor/core'
import { StatusBar, Style } from '@capacitor/status-bar'
import { registerSW } from 'virtual:pwa-register'

import App from './App.vue'
import router from './router'
import i18n from './i18n'
import { createAnalyticsPlugin } from './plugins/analytics'
import './style.css'

// Register service worker for PWA
const updateSW = registerSW({
  onNeedRefresh() {
    console.log('New content available, please refresh.')
    // Show update notification to user
    if (confirm('New version available! Reload to update?')) {
      updateSW(true)
    }
  },
  onOfflineReady() {
    console.log('App ready to work offline.')
  },
})

// Manual service worker registration as fallback
const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js')
      console.log('Service Worker registered successfully:', registration)

      // Handle updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('New service worker installed')
            }
          })
        }
      })
    } catch (error) {
      console.error('Service Worker registration failed:', error)
    }
  }
}

// PWA install prompt
let deferredPrompt: any = null

window.addEventListener('beforeinstallprompt', (e) => {
  console.log('PWA install prompt available')
  e.preventDefault()
  deferredPrompt = e

  // Show custom install button or notification
  const installEvent = new CustomEvent('pwa-install-available')
  window.dispatchEvent(installEvent)
})

window.addEventListener('appinstalled', () => {
  console.log('PWA was installed')
  deferredPrompt = null

  // Track installation
  const installEvent = new CustomEvent('pwa-installed')
  window.dispatchEvent(installEvent)
})

// Function to trigger PWA install
window.installPWA = async () => {
  if (deferredPrompt) {
    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    console.log('PWA install outcome:', outcome)
    deferredPrompt = null
  }
}

// Initialize Capacitor plugins
const initializeCapacitor = async () => {
  if (Capacitor.isNativePlatform()) {
    // Configure status bar for native platforms
    await StatusBar.setStyle({ style: Style.Default })
    await StatusBar.setBackgroundColor({ color: '#2563eb' })
  }
}

// Create Vue app
const app = createApp(App)

// Use Pinia for state management
app.use(createPinia())

// Use Vue Router
app.use(router)

// Use i18n for internationalization
app.use(i18n)

// Use analytics plugin
app.use(createAnalyticsPlugin({
  ga_measurement_id: import.meta.env.VITE_GA_MEASUREMENT_ID,
  debug: import.meta.env.DEV,
  auto_track_page_views: true,
  track_user_timing: true
}), { router })

// Initialize Capacitor
initializeCapacitor().catch(console.error)

// Register service worker
registerServiceWorker().catch(console.error)

// Mount the app
app.mount('#app')

// Global error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
}

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
})

// Log app initialization
console.log('PWA POS System initialized')
console.log('Platform:', Capacitor.getPlatform())
console.log('Native:', Capacitor.isNativePlatform())
