<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <h2 class="text-2xl font-bold text-gray-900">📊 Analytics Dashboard</h2>
      
      <!-- Timeframe Selector -->
      <select 
        v-model="selectedTimeframe" 
        @change="loadAnalytics"
        class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="1d">Last 24 Hours</option>
        <option value="7d">Last 7 Days</option>
        <option value="30d">Last 30 Days</option>
        <option value="90d">Last 90 Days</option>
      </select>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Analytics Content -->
    <div v-else-if="analyticsData" class="space-y-6">
      <!-- Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-sm border">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Events</p>
              <p class="text-2xl font-bold text-gray-900">{{ analyticsData.overview.total_events.toLocaleString() }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Sessions</p>
              <p class="text-2xl font-bold text-gray-900">{{ analyticsData.overview.total_sessions.toLocaleString() }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Unique Users</p>
              <p class="text-2xl font-bold text-gray-900">{{ analyticsData.overview.unique_users.toLocaleString() }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border">
          <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Conversion Rate</p>
              <p class="text-2xl font-bold text-gray-900">{{ analyticsData.overview.conversion_rate }}%</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Row -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top Events -->
        <div class="bg-white p-6 rounded-lg shadow-sm border">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Events</h3>
          <div class="space-y-3">
            <div 
              v-for="event in analyticsData.top_events.slice(0, 8)" 
              :key="event.event_name"
              class="flex justify-between items-center"
            >
              <span class="text-sm text-gray-600">{{ formatEventName(event.event_name) }}</span>
              <span class="text-sm font-medium text-gray-900">{{ event.count.toLocaleString() }}</span>
            </div>
          </div>
        </div>

        <!-- Top Pages -->
        <div class="bg-white p-6 rounded-lg shadow-sm border">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Pages</h3>
          <div class="space-y-3">
            <div 
              v-for="page in analyticsData.top_pages.slice(0, 8)" 
              :key="page.url"
              class="flex justify-between items-center"
            >
              <span class="text-sm text-gray-600 truncate">{{ page.url }}</span>
              <span class="text-sm font-medium text-gray-900">{{ page.views.toLocaleString() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Device Breakdown -->
      <div class="bg-white p-6 rounded-lg shadow-sm border">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Device Breakdown</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div 
            v-for="(count, device) in analyticsData.device_breakdown" 
            :key="device"
            class="text-center"
          >
            <div class="text-2xl font-bold text-gray-900">{{ count.toLocaleString() }}</div>
            <div class="text-sm text-gray-600 capitalize">{{ device }}</div>
          </div>
        </div>
      </div>

      <!-- Real-time Data -->
      <div v-if="realtimeData" class="bg-white p-6 rounded-lg shadow-sm border">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Real-time Activity</h3>
          <span class="text-sm text-gray-500">Last 30 minutes</span>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600">{{ realtimeData.active_users }}</div>
            <div class="text-sm text-gray-600">Active Users</div>
          </div>
          
          <div class="md:col-span-2">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Recent Events</h4>
            <div class="space-y-1 max-h-32 overflow-y-auto">
              <div 
                v-for="event in realtimeData.recent_events.slice(0, 5)" 
                :key="event.timestamp"
                class="text-xs text-gray-600 flex justify-between"
              >
                <span>{{ formatEventName(event.event_name) }}</span>
                <span>{{ formatTime(event.timestamp) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-12">
      <div class="text-red-600 mb-2">❌ Failed to load analytics</div>
      <div class="text-gray-600 text-sm">{{ error }}</div>
      <button 
        @click="loadAnalytics" 
        class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Retry
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const loading = ref(true)
const error = ref<string | null>(null)
const analyticsData = ref<any>(null)
const realtimeData = ref<any>(null)
const selectedTimeframe = ref('7d')

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'https://posterpos.netlify.app'

let realtimeInterval: number | null = null

const loadAnalytics = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await fetch(`${API_BASE_URL}/analytics/dashboard?timeframe=${selectedTimeframe.value}`)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    
    if (data.success) {
      analyticsData.value = data.data
    } else {
      throw new Error(data.error || 'Failed to load analytics')
    }
  } catch (err: any) {
    console.error('Analytics error:', err)
    error.value = err.message || 'Failed to load analytics'
  } finally {
    loading.value = false
  }
}

const loadRealtimeData = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/analytics/realtime`)
    
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        realtimeData.value = data.data
      }
    }
  } catch (err) {
    console.debug('Realtime analytics error:', err)
  }
}

const formatEventName = (eventName: string): string => {
  return eventName
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
}

const formatTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleTimeString()
}

onMounted(() => {
  loadAnalytics()
  loadRealtimeData()
  
  // Update realtime data every 30 seconds
  realtimeInterval = window.setInterval(loadRealtimeData, 30000)
})

onUnmounted(() => {
  if (realtimeInterval) {
    clearInterval(realtimeInterval)
  }
})
</script>
