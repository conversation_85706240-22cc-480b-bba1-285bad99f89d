# 🌐 Shared Hosting Deployment Guide

Deploy your PWA POS Shop backend to shared hosting with MySQL database.

## 🎯 Shared Hosting Options

### **Option 1: Keep Prisma with MySQL (Recommended)**
- ✅ Modern ORM with type safety
- ✅ Easy migrations and schema management
- ✅ Works with most shared hosting providers
- ⚠️ Requires Node.js support

### **Option 2: Pure MySQL (Maximum Compatibility)**
- ✅ Works on any shared hosting with MySQL
- ✅ No external dependencies
- ✅ Direct SQL queries
- ⚠️ More manual database management

## 🔧 Option 1: Prisma + MySQL Setup

### 1. Update Database Configuration

Your `server/prisma/schema.prisma` is already updated for MySQL:
```prisma
datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}
```

### 2. Environment Variables

Create `.env` in your server folder:
```env
# MySQL Database (from your hosting provider)
DATABASE_URL="mysql://username:password@host:port/database_name"

# Server Configuration
PORT=3001
NODE_ENV=production

# CORS (your frontend URL)
CORS_ORIGIN=https://your-site.netlify.app

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=public/images
```

### 3. Install MySQL Dependencies

```bash
cd server
npm install mysql2
npm install prisma @prisma/client
```

### 4. Deploy to Shared Hosting

**Popular Shared Hosting with Node.js:**
- **Hostinger** - Node.js hosting with MySQL
- **A2 Hosting** - Node.js support
- **InMotion Hosting** - Node.js plans
- **SiteGround** - Node.js hosting

**Deployment Steps:**
1. Upload server files via FTP/cPanel File Manager
2. Install dependencies: `npm install`
3. Generate Prisma client: `npx prisma generate`
4. Run migrations: `npx prisma migrate deploy`
5. Start server: `npm start`

## 🔧 Option 2: Pure MySQL Setup

### 1. Database Setup

Upload and run `server/database/mysql-setup.sql` in your hosting cPanel:

1. **Go to cPanel → MySQL Databases**
2. **Create Database**: `pwa_pos_shop`
3. **Create User**: with full privileges
4. **Import SQL**: Upload `mysql-setup.sql`

### 2. Update Package.json

```json
{
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "multer": "^1.4.5-lts.1",
    "mysql2": "^3.6.5",
    "nodemailer": "^6.9.7"
  }
}
```

### 3. Update Server Index.js

Replace Prisma imports with MySQL:
```javascript
import express from 'express'
import cors from 'cors'
import { testConnection } from './database/mysql-connection.js'
import bannersRouter from './routes/banners-mysql.js'

const app = express()

// Test database connection
testConnection()

// Use MySQL-based routes
app.use('/api/banners', bannersRouter)
```

### 4. Environment Variables

```env
# MySQL Database
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=pwa_pos_shop

# Server Configuration
PORT=3001
NODE_ENV=production
CORS_ORIGIN=https://your-site.netlify.app
```

## 🌐 Shared Hosting Providers

### **Budget-Friendly Options:**

#### **1. Hostinger ($2-5/month)**
- ✅ Node.js support
- ✅ MySQL databases
- ✅ SSL certificates
- ✅ cPanel access
- 🔗 [hostinger.com](https://hostinger.com)

#### **2. A2 Hosting ($3-7/month)**
- ✅ Node.js hosting
- ✅ MySQL/MariaDB
- ✅ SSH access
- ✅ Free SSL
- 🔗 [a2hosting.com](https://a2hosting.com)

#### **3. InMotion Hosting ($4-8/month)**
- ✅ Node.js support
- ✅ MySQL databases
- ✅ cPanel
- ✅ Free domain
- 🔗 [inmotionhosting.com](https://inmotionhosting.com)

### **Premium Options:**

#### **1. SiteGround ($4-12/month)**
- ✅ Excellent performance
- ✅ Node.js support
- ✅ Advanced caching
- ✅ Daily backups
- 🔗 [siteground.com](https://siteground.com)

#### **2. DigitalOcean App Platform ($5-12/month)**
- ✅ Modern deployment
- ✅ Auto-scaling
- ✅ Database included
- ✅ Git integration
- 🔗 [digitalocean.com](https://digitalocean.com)

## 📋 Deployment Checklist

### **Before Deployment:**
- [ ] Choose hosting provider with Node.js + MySQL
- [ ] Prepare database credentials
- [ ] Update environment variables
- [ ] Test locally with MySQL

### **During Deployment:**
- [ ] Upload server files
- [ ] Install dependencies (`npm install`)
- [ ] Create MySQL database
- [ ] Import database schema
- [ ] Configure environment variables
- [ ] Start Node.js application

### **After Deployment:**
- [ ] Test API endpoints
- [ ] Verify database connection
- [ ] Check file upload functionality
- [ ] Test CORS with frontend
- [ ] Monitor error logs

## 🔧 Database Management

### **With Prisma:**
```bash
# Generate client
npx prisma generate

# Run migrations
npx prisma migrate deploy

# View database
npx prisma studio
```

### **With Pure MySQL:**
```bash
# Connect to database
mysql -h host -u user -p database_name

# Import schema
mysql -h host -u user -p database_name < mysql-setup.sql

# Backup database
mysqldump -h host -u user -p database_name > backup.sql
```

## 🚨 Troubleshooting

### **Common Issues:**

#### **1. Node.js Not Supported**
- **Solution**: Choose different hosting provider
- **Alternative**: Use PHP backend instead

#### **2. Database Connection Fails**
- Check database credentials
- Verify host/port settings
- Ensure database exists
- Check firewall settings

#### **3. File Upload Issues**
- Verify upload directory permissions
- Check file size limits
- Ensure multer configuration

#### **4. CORS Errors**
- Update CORS_ORIGIN environment variable
- Check frontend URL matches exactly
- Verify protocol (http vs https)

### **Debug Commands:**
```bash
# Test database connection
node -e "import('./database/mysql-connection.js').then(db => db.testConnection())"

# Check environment variables
node -e "console.log(process.env)"

# Test API endpoint
curl https://your-domain.com/api/banners
```

## 📊 Performance Optimization

### **Database Optimization:**
- Use connection pooling
- Add proper indexes
- Optimize queries
- Regular database maintenance

### **Server Optimization:**
- Enable gzip compression
- Use caching headers
- Optimize image uploads
- Monitor memory usage

### **Shared Hosting Tips:**
- Use CDN for static files
- Optimize database queries
- Implement proper error handling
- Monitor resource usage

## 🔐 Security Considerations

### **Database Security:**
- Use strong passwords
- Limit database user privileges
- Regular security updates
- Backup data regularly

### **Server Security:**
- Validate all inputs
- Sanitize file uploads
- Use HTTPS only
- Implement rate limiting

## 💰 Cost Comparison

| Provider | Price/Month | Node.js | MySQL | SSL | Support |
|----------|-------------|---------|-------|-----|---------|
| Hostinger | $2-5 | ✅ | ✅ | ✅ | Good |
| A2 Hosting | $3-7 | ✅ | ✅ | ✅ | Excellent |
| InMotion | $4-8 | ✅ | ✅ | ✅ | Good |
| SiteGround | $4-12 | ✅ | ✅ | ✅ | Excellent |
| DigitalOcean | $5-12 | ✅ | ✅ | ✅ | Good |

## 🎯 Recommendation

**For beginners**: Start with **Hostinger** or **A2 Hosting**
**For performance**: Choose **SiteGround** or **DigitalOcean**
**For budget**: **Hostinger** offers the best value

Your PWA POS Shop backend will work perfectly on shared hosting! 🚀
