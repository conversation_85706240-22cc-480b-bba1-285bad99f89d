# 🔧 Sync Troubleshooting Guide

Your database is imported but Full Sync isn't working? Let's fix it step by step!

## 🚨 **Quick Fix Steps**

### **Step 1: Test Database Connection**
```bash
cd server
node test-database.js
```

**Expected Output:**
```
✅ Database connection successful!
✅ Categories table: 4 records
✅ Banners table: 1 records
✅ Branches table: 6 records
```

### **Step 2: Fix Prisma Connection**
```bash
node update-prisma-schema.js
```

This will:
- ✅ Update Prisma schema for MySQL
- ✅ Generate Prisma client
- ✅ Sync database schema
- ✅ Test connection

### **Step 3: Test Sync Functionality**
```bash
node fix-database-sync.js
```

This will:
- ✅ Test database connection
- ✅ Test Poster POS API
- ✅ Check database schema
- ✅ Perform test sync

### **Step 4: Manual Sync (If Prisma Fails)**
```bash
node manual-sync.js
```

This will:
- ✅ Direct MySQL connection
- ✅ Sync categories from Poster POS
- ✅ Sync products from Poster POS
- ✅ Sync inventory levels

## 🔍 **Diagnostic Commands**

### **Check Database Tables:**
```bash
node -e "
import mysql from 'mysql2/promise';
const conn = await mysql.createConnection({
  host: 'avalon.cityhost.com.ua',
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos'
});
const [tables] = await conn.execute('SHOW TABLES');
console.log('Tables:', tables.map(t => Object.values(t)[0]));
await conn.end();
"
```

### **Check Prisma Client:**
```bash
npx prisma generate
npx prisma db push
```

### **Test Poster API:**
```bash
curl "https://joinposter.com/api/menu.getCategories?token=218047:05891220e474bad7f26b6eaa0be3f344"
```

## 🛠️ **Common Issues & Solutions**

### **Issue 1: "Prisma Client Not Generated"**

**Solution:**
```bash
cd server
npx prisma generate
npm restart
```

### **Issue 2: "Database Connection Failed"**

**Check:**
1. Database credentials in `.env`
2. MySQL server is running
3. Network connectivity

**Fix:**
```bash
node test-database.js
```

### **Issue 3: "No Data After Sync"**

**Possible Causes:**
- Prisma schema mismatch
- Database table structure issues
- API connection problems

**Solution:**
```bash
# Try manual sync first
node manual-sync.js

# If that works, fix Prisma
node update-prisma-schema.js
```

### **Issue 4: "Poster API Not Responding"**

**Check:**
```bash
curl -I https://joinposter.com/api/menu.getCategories?token=218047:05891220e474bad7f26b6eaa0be3f344
```

**Should return:** `HTTP/1.1 200 OK`

### **Issue 5: "Schema Mismatch"**

**Fix:**
```bash
npx prisma db pull  # Pull current database schema
npx prisma generate # Generate new client
```

## 📊 **Expected Results After Fix**

### **Categories Table:**
- Should have Poster POS categories
- Plus your original 4 sample categories

### **Products Table:**
- Should have all products from Poster POS
- With correct prices (converted from kopecks)
- Linked to proper categories

### **Inventory Table:**
- Should have stock levels for each product
- Per branch/storage location

## 🎯 **Step-by-Step Recovery**

### **If Nothing Works:**

1. **Reset Prisma:**
   ```bash
   rm -rf node_modules/.prisma
   npx prisma generate
   ```

2. **Manual Database Check:**
   ```bash
   node test-database.js
   ```

3. **Force Manual Sync:**
   ```bash
   node manual-sync.js
   ```

4. **Restart Server:**
   ```bash
   npm start
   ```

5. **Test Admin Panel:**
   - Go to `/admin`
   - Check "Products" tab
   - Should see synced products

## 🔄 **Alternative Sync Methods**

### **Method 1: Direct API Call**
```bash
curl -X POST http://localhost:3001/api/sync/full
```

### **Method 2: Manual Database Insert**
```sql
-- Connect to your database and run:
SELECT COUNT(*) FROM products;
SELECT COUNT(*) FROM categories;
```

### **Method 3: Prisma Studio**
```bash
npx prisma studio
```
- Opens web interface
- View/edit database records
- Check data integrity

## 📞 **Getting Help**

### **Check Logs:**
```bash
# Server logs
npm start

# Database logs
node test-database.js

# Sync logs
node fix-database-sync.js
```

### **Common Error Messages:**

**"PrismaClientInitializationError"**
- Run: `npx prisma generate`

**"Table doesn't exist"**
- Run: `npx prisma db push`

**"Connection refused"**
- Check database credentials
- Test: `node test-database.js`

**"API timeout"**
- Check internet connection
- Test Poster API directly

## ✅ **Success Checklist**

After running the fixes:

- [ ] Database connection works
- [ ] Prisma client generated
- [ ] Categories synced from Poster POS
- [ ] Products synced from Poster POS
- [ ] Inventory levels updated
- [ ] Admin panel shows data
- [ ] Frontend displays products

## 🚀 **Quick Recovery Script**

Run this to fix everything:

```bash
#!/bin/bash
echo "🔧 PWA POS Shop - Quick Fix"
echo "=========================="

echo "1. Testing database..."
node test-database.js

echo "2. Updating Prisma..."
node update-prisma-schema.js

echo "3. Testing sync..."
node fix-database-sync.js

echo "4. Manual sync (backup)..."
node manual-sync.js

echo "5. Restarting server..."
npm start
```

Your sync should now work perfectly! 🎉
