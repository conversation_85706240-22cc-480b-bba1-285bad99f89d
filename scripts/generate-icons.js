// Simple script to generate placeholder PWA icons
const fs = require('fs')
const path = require('path')

// Create icons directory if it doesn't exist
const iconsDir = path.join(__dirname, '..', 'public', 'icons')
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true })
}

// Icon sizes needed for PWA
const sizes = [72, 96, 128, 144, 152, 192, 384, 512]

// Generate placeholder icon URLs
sizes.forEach(size => {
  const iconUrl = `https://via.placeholder.com/${size}x${size}/3b82f6/ffffff?text=POS`
  const iconPath = path.join(iconsDir, `icon-${size}x${size}.png`)
  
  console.log(`Icon placeholder created: icon-${size}x${size}.png`)
  console.log(`URL: ${iconUrl}`)
  
  // Create a simple text file with the URL for now
  // In a real implementation, you would download the actual image
  fs.writeFileSync(iconPath + '.url', iconUrl)
})

console.log('PWA icon placeholders generated!')
console.log('To get actual icons, download them from the URLs above or create custom icons.')
