<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Branch Cache</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            display: inline-block;
            text-decoration: none;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.danger:hover {
            background-color: #c82333;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Branch Cache Management</h1>
        
        <div class="info">
            <strong>Issue:</strong> The delivery calculation is using cached branch data with old coordinates. 
            After updating branch coordinates in the database, the frontend cache needs to be cleared.
        </div>

        <div class="status" id="status"></div>

        <h3>Current Cache Status:</h3>
        <pre id="cacheStatus">Loading...</pre>

        <h3>Actions:</h3>
        <button class="button" onclick="checkCache()">🔍 Check Cache</button>
        <button class="button danger" onclick="clearCache()">🗑️ Clear Branch Cache</button>
        <button class="button" onclick="clearAllCache()">🧹 Clear All Cache</button>

        <div class="info">
            <strong>Instructions:</strong>
            <ol>
                <li>Click "Check Cache" to see current cached data</li>
                <li>Click "Clear Branch Cache" to remove only branch data</li>
                <li>Click "Clear All Cache" to remove all PWA cache</li>
                <li>Refresh the main application to load updated branch coordinates</li>
            </ol>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }

        function checkCache() {
            try {
                const branchCache = localStorage.getItem('pwa-pos-branches');
                const locationCache = localStorage.getItem('pwa-pos-location');
                const cartCache = localStorage.getItem('pwa-pos-cart');
                
                let cacheInfo = 'Branch Cache:\n';
                if (branchCache) {
                    const branches = JSON.parse(branchCache);
                    cacheInfo += `- ${branches.branches?.length || 0} branches cached\n`;
                    cacheInfo += `- Last fetched: ${branches.lastFetched || 'Unknown'}\n`;
                    if (branches.branches?.length > 0) {
                        cacheInfo += `- Sample branch: ${branches.branches[0].name}\n`;
                        cacheInfo += `- Sample coordinates: ${branches.branches[0].latitude}, ${branches.branches[0].longitude}\n`;
                    }
                } else {
                    cacheInfo += '- No branch cache found\n';
                }
                
                cacheInfo += '\nLocation Cache:\n';
                if (locationCache) {
                    const location = JSON.parse(locationCache);
                    cacheInfo += `- User location: ${location.userLocation ? 'Set' : 'Not set'}\n`;
                    if (location.userLocation) {
                        cacheInfo += `- Coordinates: ${location.userLocation.latitude}, ${location.userLocation.longitude}\n`;
                    }
                } else {
                    cacheInfo += '- No location cache found\n';
                }
                
                cacheInfo += '\nCart Cache:\n';
                if (cartCache) {
                    const cart = JSON.parse(cartCache);
                    cacheInfo += `- Cart items: ${cart.items?.length || 0}\n`;
                    cacheInfo += `- Delivery method: ${cart.deliveryMethod || 'Not set'}\n`;
                } else {
                    cacheInfo += '- No cart cache found\n';
                }
                
                document.getElementById('cacheStatus').textContent = cacheInfo;
                showStatus('Cache status updated', 'success');
            } catch (error) {
                showStatus('Error checking cache: ' + error.message, 'error');
            }
        }

        function clearCache() {
            try {
                localStorage.removeItem('pwa-pos-branches');
                showStatus('Branch cache cleared successfully! Please refresh the main application.', 'success');
                checkCache();
            } catch (error) {
                showStatus('Error clearing branch cache: ' + error.message, 'error');
            }
        }

        function clearAllCache() {
            try {
                // Clear all PWA-related cache
                const keys = Object.keys(localStorage);
                const pwaKeys = keys.filter(key => key.startsWith('pwa-pos-'));
                
                pwaKeys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                showStatus(`Cleared ${pwaKeys.length} cache entries. Please refresh the main application.`, 'success');
                checkCache();
            } catch (error) {
                showStatus('Error clearing all cache: ' + error.message, 'error');
            }
        }

        // Load cache status on page load
        window.onload = function() {
            checkCache();
        };
    </script>
</body>
</html>
