// Script to fix product categorization using direct SQL approach
const mysql = require('mysql2/promise')

// Database connection using production credentials
const dbConfig = {
  host: 'sql11.freesqldatabase.com',
  user: 'sql11751617',
  password: 'YdGNJGJGJG',
  database: 'sql11751617',
  port: 3306
}

// Smart categorization patterns
const patterns = {
  'Пиво Скло Опілля': ['опілля', 'скло', 'пляшка'],
  'Пиво розлив': ['розлив', 'draft', 'кег'],
  'Пиво металева банка Опілля': ['банка', 'металева', 'опілля'],
  'Пиво ПЕТ Опілля': ['пет', 'пластик', 'опілля'],
  'пиво ж/б імпорт': ['імпорт', 'import', 'жб'],
  'Вино': ['вино', 'wine', 'червоне', 'біле'],
  'Сидр': ['сидр', 'cider'],
  'Закуски Вагові': ['ваг', 'вагов', 'кг', 'грам'],
  'Закуски штучні': ['штучн', 'шт', 'piece', 'арахіс', 'сир', 'ікра', 'горіх'],
  'Напої б/а': ['кола', 'пепсі', 'фанта', 'спрайт', 'сік', 'вода'],
  'Тара': ['тара', 'пляшка порожня', 'кег порожній', 'кришка', 'стаканчик'],
  'Інші': ['інш', 'other', 'різне']
}

async function fixCategories() {
  let connection
  try {
    console.log('🔄 Connecting to database...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    console.log('🔄 Fetching categories...')
    const [categories] = await connection.execute('SELECT id, name FROM categories')
    console.log(`📊 Found ${categories.length} categories`)

    console.log('🔄 Fetching products...')
    const [products] = await connection.execute('SELECT id, name, category_id FROM products')
    console.log(`📊 Found ${products.length} products`)

    // Create category lookup
    const categoryLookup = {}
    categories.forEach(cat => {
      categoryLookup[cat.name] = cat.id
    })

    let updates = 0
    const updatePromises = []

    for (const product of products) {
      const productName = (product.name || '').toLowerCase()
      let newCategoryId = null

      // Try to match product name to category patterns
      for (const [categoryName, keywords] of Object.entries(patterns)) {
        if (keywords.some(keyword => productName.includes(keyword))) {
          if (categoryLookup[categoryName]) {
            newCategoryId = categoryLookup[categoryName]
            console.log(`📝 "${product.name}" → ${categoryName}`)
            break
          }
        }
      }

      // If still no category, try beer-related patterns
      if (!newCategoryId && (productName.includes('пиво') || productName.includes('beer'))) {
        if (categoryLookup['Пиво Скло Опілля']) {
          newCategoryId = categoryLookup['Пиво Скло Опілля']
          console.log(`📝 "${product.name}" → Пиво Скло Опілля (beer fallback)`)
        }
      }

      // Default to snacks for food items
      if (!newCategoryId && (productName.includes('сир') || productName.includes("м'ясо") ||
                             productName.includes('риба') || productName.includes('ікра') ||
                             productName.includes('арахіс') || productName.includes('горіх'))) {
        if (categoryLookup['Закуски штучні']) {
          newCategoryId = categoryLookup['Закуски штучні']
          console.log(`📝 "${product.name}" → Закуски штучні (food fallback)`)
        }
      }

      // Final fallback to general beverages
      if (!newCategoryId) {
        if (categoryLookup['Напої']) {
          newCategoryId = categoryLookup['Напої']
        }
      }

      // Update product if category changed
      if (newCategoryId && newCategoryId !== product.category_id) {
        const updatePromise = connection.execute(
          'UPDATE products SET category_id = ? WHERE id = ?',
          [newCategoryId, product.id]
        ).then(() => {
          updates++
        }).catch(error => {
          console.error(`❌ Failed to update ${product.name}:`, error.message)
        })

        updatePromises.push(updatePromise)
      }
    }

    console.log('🔄 Executing updates...')
    await Promise.all(updatePromises)

    // Get final category counts
    console.log('📊 Final category distribution:')
    const [finalCounts] = await connection.execute(`
      SELECT
        c.name as category_name,
        COUNT(p.id) as product_count
      FROM categories c
      LEFT JOIN products p ON c.id = p.category_id
      GROUP BY c.id, c.name
      ORDER BY product_count DESC
    `)

    finalCounts.forEach(row => {
      console.log(`  ${row.category_name}: ${row.product_count} products`)
    })

    console.log(`✅ Updated ${updates} products successfully!`)

  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Database connection closed')
    }
  }
}

// Run the fix
fixCategories()
