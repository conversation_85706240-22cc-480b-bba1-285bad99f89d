-- PWA POS Shop Database Schema
-- Database: ch6edd8920_pwapos
-- Host: avalon.cityhost.com.ua
-- Created for CityHost.com.ua hosting

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `categories`
CREATE TABLE `categories` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `name` varchar(255) NOT NULL,
  `description` text,
  `image_url` varchar(500) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `poster_id` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `products`
CREATE TABLE `products` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `name` varchar(255) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `category_id` varchar(36) DEFAULT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `stock_quantity` decimal(8,3) DEFAULT 0.000,
  `min_quantity` decimal(8,3) DEFAULT 1.000,
  `unit` varchar(50) DEFAULT 'шт',
  `poster_id` varchar(100) DEFAULT NULL,
  `attributes` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `branches`
CREATE TABLE `branches` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `name` varchar(255) NOT NULL,
  `address` text,
  `phone` varchar(50) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `working_hours` text,
  `is_active` tinyint(1) DEFAULT 1,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `poster_id` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `customers`
CREATE TABLE `customers` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `address` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `orders`
CREATE TABLE `orders` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `customer_id` varchar(36) DEFAULT NULL,
  `branch_id` varchar(36) DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `delivery_fee` decimal(10,2) DEFAULT 0.00,
  `delivery_method` enum('delivery','pickup') NOT NULL,
  `delivery_address` text,
  `status` enum('pending','confirmed','preparing','ready','delivered','cancelled') DEFAULT 'pending',
  `payment_status` enum('pending','paid','failed','refunded') DEFAULT 'pending',
  `notes` text,
  `poster_order_id` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `order_items`
CREATE TABLE `order_items` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `order_id` varchar(36) NOT NULL,
  `product_id` varchar(36) DEFAULT NULL,
  `product_name` varchar(255) NOT NULL,
  `quantity` decimal(8,3) NOT NULL DEFAULT 1.000,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `banners`
CREATE TABLE `banners` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `title` varchar(255) NOT NULL,
  `subtitle` text,
  `image_url` varchar(500) DEFAULT NULL,
  `link_url` varchar(500) DEFAULT NULL,
  `link_text` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `analytics_events`
CREATE TABLE `analytics_events` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `event_name` varchar(255) NOT NULL,
  `category` varchar(255) NOT NULL,
  `action` varchar(255) NOT NULL,
  `label` varchar(255) DEFAULT NULL,
  `value` decimal(10,2) DEFAULT NULL,
  `custom_parameters` json DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `user_id` varchar(36) DEFAULT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `url` varchar(500) DEFAULT NULL,
  `referrer` varchar(500) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `analytics_sessions`
CREATE TABLE `analytics_sessions` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `session_id` varchar(255) NOT NULL,
  `user_id` varchar(36) DEFAULT NULL,
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `duration` int(11) DEFAULT NULL,
  `page_views` int(11) DEFAULT 0,
  `events_count` int(11) DEFAULT 0,
  `device_info` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `product_inventory`
CREATE TABLE `product_inventory` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `product_id` varchar(36) NOT NULL,
  `branch_id` varchar(36) NOT NULL,
  `quantity` decimal(8,3) DEFAULT 0.000,
  `reserved_quantity` decimal(8,3) DEFAULT 0.000,
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Table structure for table `site_config`
CREATE TABLE `site_config` (
  `id` varchar(36) NOT NULL DEFAULT (uuid()),
  `config_key` varchar(255) NOT NULL,
  `config_value` json DEFAULT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

-- Indexes for dumped tables

-- Indexes for table `categories`
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_categories_active` (`is_active`),
  ADD KEY `idx_categories_sort` (`sort_order`);

-- Indexes for table `products`
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_products_category` (`category_id`),
  ADD KEY `idx_products_active` (`is_active`),
  ADD KEY `idx_products_poster` (`poster_id`);

-- Indexes for table `branches`
ALTER TABLE `branches`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_branches_active` (`is_active`),
  ADD KEY `idx_branches_poster` (`poster_id`);

-- Indexes for table `customers`
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_customers_email` (`email`),
  ADD KEY `idx_customers_phone` (`phone`);

-- Indexes for table `orders`
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_orders_customer` (`customer_id`),
  ADD KEY `idx_orders_branch` (`branch_id`),
  ADD KEY `idx_orders_status` (`status`),
  ADD KEY `idx_orders_created` (`created_at`);

-- Indexes for table `order_items`
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_order_items_order` (`order_id`),
  ADD KEY `idx_order_items_product` (`product_id`);

-- Indexes for table `banners`
ALTER TABLE `banners`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_banners_active` (`is_active`),
  ADD KEY `idx_banners_sort` (`sort_order`);

-- Indexes for table `analytics_events`
ALTER TABLE `analytics_events`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_analytics_events_timestamp` (`timestamp`),
  ADD KEY `idx_analytics_events_event_name` (`event_name`),
  ADD KEY `idx_analytics_events_session` (`session_id`),
  ADD KEY `idx_analytics_events_user` (`user_id`);

-- Indexes for table `analytics_sessions`
ALTER TABLE `analytics_sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `session_id` (`session_id`),
  ADD KEY `idx_analytics_sessions_start` (`start_time`),
  ADD KEY `idx_analytics_sessions_user` (`user_id`);

-- Indexes for table `product_inventory`
ALTER TABLE `product_inventory`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_product_branch` (`product_id`,`branch_id`),
  ADD KEY `idx_inventory_product` (`product_id`),
  ADD KEY `idx_inventory_branch` (`branch_id`);

-- Indexes for table `site_config`
ALTER TABLE `site_config`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `config_key` (`config_key`);

-- --------------------------------------------------------

-- Sample data for categories
INSERT INTO `categories` (`id`, `name`, `description`, `sort_order`, `is_active`) VALUES
('cat_beverages', 'Напої', 'Безалкогольні та алкогольні напої', 1, 1),
('cat_snacks', 'Закуски', 'Снеки та закуски до напоїв', 2, 1),
('cat_main_dishes', 'Основні страви', 'Гарячі страви та основні блюда', 3, 1),
('cat_desserts', 'Десерти', 'Солодощі та десерти', 4, 1);

-- Sample data for branches (Opillia locations in Kyiv)
INSERT INTO `branches` (`id`, `name`, `address`, `phone`, `working_hours`, `is_active`, `latitude`, `longitude`) VALUES
('branch_center', 'Опілля - Центр', 'вул. Костянтина Данькевича, 10, Київ', '+38 (097) 324 46 68', '10:00-22:00 щодня', 1, 50.4501, 30.5234),
('branch_podil', 'Опілля - Поділ', 'вул. Сагайдачного, 25, Київ', '+38 (097) 324 46 68', '10:00-22:00 щодня', 1, 50.4676, 30.5169),
('branch_pechersk', 'Опілля - Печерськ', 'вул. Бессарабська, 5, Київ', '+38 (097) 324 46 68', '10:00-22:00 щодня', 1, 50.4433, 30.5236),
('branch_obolon', 'Опілля - Оболонь', 'просп. Оболонський, 15, Київ', '+38 (097) 324 46 68', '10:00-22:00 щодня', 1, 50.5167, 30.4983),
('branch_troieshchyna', 'Опілля - Троєщина', 'вул. Лаврухіна, 8, Київ', '+38 (097) 324 46 68', '10:00-22:00 щодня', 1, 50.5234, 30.6345),
('branch_darnytsia', 'Опілля - Дарниця', 'вул. Харківське шосе, 201, Київ', '+38 (097) 324 46 68', '10:00-22:00 щодня', 1, 50.4089, 30.6344);

-- Sample data for site configuration
INSERT INTO `site_config` (`config_key`, `config_value`, `description`) VALUES
('site_title', '"Опілля - PWA POS Shop"', 'Назва сайту'),
('site_description', '"Найкращі напої та закуски з доставкою по Києву"', 'Опис сайту'),
('delivery_fee_base', '99', 'Базова вартість доставки в грн'),
('delivery_fee_per_km', '30', 'Додаткова плата за км в грн'),
('min_order_amount', '300', 'Мінімальна сума замовлення в грн'),
('contact_phone', '"+38 (097) 324 46 68"', 'Контактний телефон'),
('contact_email', '"<EMAIL>"', 'Контактний email'),
('working_hours', '"10:00-22:00 щодня"', 'Години роботи'),
('company_address', '"вулиця Костянтина Данькевича, 10, Київ"', 'Адреса компанії');

-- Sample banner
INSERT INTO `banners` (`id`, `title`, `subtitle`, `is_active`, `sort_order`) VALUES
('banner_welcome', 'Ласкаво просимо до Опілля!', 'Найкращі напої та закуски з доставкою по Києву. Замовляйте онлайн!', 1, 1);

COMMIT;
