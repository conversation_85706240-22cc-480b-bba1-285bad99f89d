# 🚀 PWA POS Shop - Monorepo Netlify Deployment

## 📋 Quick Deployment Steps

### 1. Install Dependencies
```bash
npm install
```

### 2. Deploy to Netlify
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy
netlify deploy --prod
```

## 🎯 What's Included

### Frontend (Vue.js PWA)
- **Shop Interface**: `/shop`
- **Admin Panel**: `/admin`
- **PWA Features**: Offline support, installable
- **Responsive Design**: Mobile-first

### Backend (Netlify Functions)
- **API Endpoints**: `/api/*`
- **Database**: MySQL integration
- **Poster POS Sync**: Real-time product sync
- **Order Management**: Complete order processing

## 🔧 Configuration Files

### `netlify.toml`
- ✅ Functions configuration
- ✅ Redirects for SPA routing
- ✅ API routing to functions
- ✅ CORS headers
- ✅ Security headers

### `netlify/functions/api.js`
- ✅ Express.js serverless function
- ✅ MySQL database connection
- ✅ Poster POS API integration
- ✅ All API endpoints

### `package.json`
- ✅ Build scripts for monorepo
- ✅ Frontend and backend dependencies
- ✅ Netlify deployment commands

## 🌐 API Endpoints

After deployment, your API will be available at:
```
https://your-site.netlify.app/api/health
https://your-site.netlify.app/api/categories
https://your-site.netlify.app/api/products
https://your-site.netlify.app/api/branches
https://your-site.netlify.app/api/sync/full
https://your-site.netlify.app/api/orders
https://your-site.netlify.app/api/banners
```

## 🧪 Testing After Deployment

### 1. Test Health Check
```bash
curl https://your-site.netlify.app/api/health
```

### 2. Test Sync
```bash
curl -X POST https://your-site.netlify.app/api/sync/full
```

### 3. Test Frontend
Visit: `https://your-site.netlify.app`

## 🔄 Sync Products

1. **Go to Admin**: `https://your-site.netlify.app/admin`
2. **Click Sync**: Import data from Poster POS
3. **Check Database**: Verify products in MySQL

## 📱 PWA Features

- ✅ **Offline Support**: Service worker caching
- ✅ **Installable**: Add to home screen
- ✅ **Push Notifications**: Order updates
- ✅ **Geolocation**: Delivery addresses
- ✅ **Responsive**: Works on all devices

## 🚀 Continuous Deployment

Connect to GitHub for automatic deployments:
1. **Netlify Dashboard** → **New site from Git**
2. **Select Repository**: `waverhan/posterpos-shop`
3. **Build Settings**:
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Functions directory: `netlify/functions`

## 🎉 Success!

Your PWA POS Shop is now deployed with:
- ✅ **Monorepo Structure**: Frontend + Backend together
- ✅ **Serverless Functions**: Scalable API
- ✅ **MySQL Integration**: Real database
- ✅ **Poster POS Sync**: Live product data
- ✅ **PWA Capabilities**: Modern web app features
- ✅ **Auto Deployment**: GitHub integration

**Live URLs:**
- **Shop**: `https://your-site.netlify.app/shop`
- **Admin**: `https://your-site.netlify.app/admin`
- **API**: `https://your-site.netlify.app/api/health`
