# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Vite
dist/
dist-ssr/
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Capacitor
android/
ios/
electron/

# Security - API Keys and Sensitive Data
*.key
*.pem
*.p12
*.p8
*.mobileprovision
google-services.json
GoogleService-Info.plist

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
temp/
tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.swp
*.swo
*~

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Local configuration files
config.local.js
config.local.json
settings.local.json

# Test files
test-results/
coverage/
.nyc_output/

# Build artifacts
build/
dist/
out/

# Package manager lock files (choose one)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Local development
.local
.dev

# Netlify
.netlify/

# Vercel
.vercel

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Supabase
.supabase/

# Sentry
.sentryclirc

# Environment-specific files
.env.staging
.env.production
.env.development

# API documentation
api-docs/

# Certificates
*.crt
*.cert
*.ca-bundle

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# AWS
.aws/

# Azure
.azure/

# Google Cloud
.gcloud/

# Monitoring and analytics
.sentry-native/

# Performance monitoring
.lighthouse/

# User uploads (if storing locally)
uploads/
user-content/
media/

# Generated documentation
docs/build/
docs/dist/

# Translation files
*.pot

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# Poetry
poetry.lock

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Local development overrides
*.local.*
local.*

# Backup and temporary files
*~
*.swp
*.swo
.#*
\#*#

# macOS
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# PWA POS Shop - Server Files (exclude old server directory)
server/
server-old/
backend-old/

# PWA POS Shop - Temporary and Debug Files
debug-*.js
test-*.js
manual-sync.js
test-sync-*.js
minimal-server.js
start-shared-hosting.js
shared-hosting-package.json
netlify-backend-package.json

# PWA POS Shop - Local Development
.env.local
.env.backup
package.json.backup

# PWA POS Shop - Build and Deployment
.netlify/
dist-backup/
build-backup/

# PWA POS Shop - Database and Uploads
public/images/products/*
public/images/banners/*
!public/images/products/.gitkeep
!public/images/banners/.gitkeep

# PWA POS Shop - License and Security
license-server/
*.license
api-keys.json
