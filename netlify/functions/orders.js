// Simple orders endpoint that returns mock data for now
exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
    'Content-Type': 'application/json'
  }

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' }
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ success: false, error: 'Method not allowed' })
    }
  }

  try {
    // Return mock orders data for now
    const orders = [
      {
        id: 1,
        customer_name: '<PERSON>',
        total: 150.00,
        status: 'completed',
        created_at: new Date().toISOString()
      },
      {
        id: 2,
        customer_name: '<PERSON>',
        total: 89.50,
        status: 'pending',
        created_at: new Date().toISOString()
      }
    ]
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        orders: orders
      })
    }
  } catch (error) {
    console.error('Orders error:', error)
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        success: false, 
        error: 'Failed to fetch orders',
        message: error.message 
      })
    }
  }
}
