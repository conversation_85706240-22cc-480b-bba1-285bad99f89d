// Categories endpoint
import mysql from 'mysql2/promise'

const dbConfig = {
  host: 'avalon.cityhost.com.ua',
  port: 3306,
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos',
  charset: 'utf8mb4'
}

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
  'Content-Type': 'application/json'
}

export const handler = async (event, context) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' }
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ success: false, error: 'Method not allowed' })
    }
  }

  try {
    const connection = await mysql.createConnection(dbConfig)
    const [categories] = await connection.execute('SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order')
    await connection.end()
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        categories: categories
      })
    }
  } catch (error) {
    console.error('Categories error:', error)
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        success: false, 
        error: 'Failed to fetch categories',
        message: error.message 
      })
    }
  }
}
