// Simplified Netlify Function for PWA POS API
import mysql from 'mysql2/promise'

// Database configuration
const dbConfig = {
  host: 'avalon.cityhost.com.ua',
  port: 3306,
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos',
  charset: 'utf8mb4'
}

const POSTER_API_BASE = 'https://joinposter.com/api'
const POSTER_TOKEN = '218047:05891220e474bad7f26b6eaa0be3f344'

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
  'Content-Type': 'application/json'
}

// Generate simple ID
function generateId() {
  return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

export const handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    }
  }

  try {
    // Extract the path from the event
    let path = event.path || ''
    const method = event.httpMethod

    // Handle different path formats
    if (path.includes('/.netlify/functions/api-simple')) {
      path = path.replace('/.netlify/functions/api-simple', '')
    }

    // Handle query parameters for path extraction
    const pathSegments = event.rawUrl ? new URL(event.rawUrl).pathname : path
    if (pathSegments && pathSegments.includes('/api/')) {
      path = pathSegments.substring(pathSegments.indexOf('/api/'))
    }

    console.log(`API Request: ${method} ${path}`)
    console.log(`Full event path: ${event.path}`)
    console.log(`Raw URL: ${event.rawUrl}`)

    // Health check - handle multiple path formats
    if (path === '/health' || path === '/api/health' || path.endsWith('/health')) {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          status: 'OK',
          message: 'PWA POS Backend on Netlify Functions',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        })
      }
    }

    // Get categories - handle multiple path formats
    if ((path === '/categories' || path === '/api/categories' || path.endsWith('/categories')) && method === 'GET') {
      const connection = await mysql.createConnection(dbConfig)
      const [categories] = await connection.execute('SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order')
      await connection.end()
      
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          categories: categories
        })
      }
    }

    // Get products - handle multiple path formats
    if ((path === '/products' || path === '/api/products' || path.endsWith('/products')) && method === 'GET') {
      const connection = await mysql.createConnection(dbConfig)
      const [products] = await connection.execute(`
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.is_active = 1 
        ORDER BY p.name
      `)
      await connection.end()
      
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          products: products
        })
      }
    }

    // Get branches - handle multiple path formats
    if ((path === '/branches' || path === '/api/branches' || path.endsWith('/branches')) && method === 'GET') {
      const connection = await mysql.createConnection(dbConfig)
      const [branches] = await connection.execute('SELECT * FROM branches WHERE is_active = 1 ORDER BY name')
      await connection.end()
      
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          branches: branches
        })
      }
    }

    // Sync endpoint - handle multiple path formats
    if ((path === '/sync/full' || path === '/api/sync/full' || path.endsWith('/sync/full') || path.includes('/sync/full')) && method === 'POST') {
      const connection = await mysql.createConnection(dbConfig)
      let results = { categories: 0, products: 0, errors: [] }
      
      try {
        // Import fetch for Node.js
        const fetch = (await import('node-fetch')).default
        
        // Sync categories
        const categoriesResponse = await fetch(`${POSTER_API_BASE}/menu.getCategories?token=${POSTER_TOKEN}`)
        const categoriesData = await categoriesResponse.json()
        
        if (categoriesData.response) {
          for (const cat of categoriesData.response) {
            try {
              await connection.execute(`
                INSERT INTO categories (id, poster_id, name, description, sort_order, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE name = VALUES(name), updated_at = NOW()
              `, [
                generateId(),
                cat.category_id,
                cat.category_name,
                `Category: ${cat.category_name}`,
                parseInt(cat.sort_order) || 0,
                1
              ])
              results.categories++
            } catch (err) {
              results.errors.push(`Category ${cat.category_name}: ${err.message}`)
            }
          }
        }
        
        // Sync products
        const productsResponse = await fetch(`${POSTER_API_BASE}/menu.getProducts?token=${POSTER_TOKEN}`)
        const productsData = await productsResponse.json()
        
        if (productsData.response) {
          for (const product of productsData.response) {
            try {
              // Find category
              const [categoryResult] = await connection.execute(
                'SELECT id FROM categories WHERE poster_id = ? LIMIT 1',
                [product.category_id]
              )
              
              let categoryId = null
              if (categoryResult.length > 0) {
                categoryId = categoryResult[0].id
              } else {
                const [firstCat] = await connection.execute('SELECT id FROM categories LIMIT 1')
                if (firstCat.length > 0) {
                  categoryId = firstCat[0].id
                }
              }
              
              if (!categoryId) continue
              
              // Calculate price
              let price = 0
              if (product.price) {
                if (typeof product.price === 'object') {
                  price = parseFloat(product.price['1'] || product.price['0'] || 0) / 100
                } else {
                  price = parseFloat(product.price) / 100
                }
              }
              
              await connection.execute(`
                INSERT INTO products (id, poster_id, category_id, name, description, price, is_active, stock_quantity, unit, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE name = VALUES(name), price = VALUES(price), updated_at = NOW()
              `, [
                generateId(),
                product.product_id,
                categoryId,
                product.product_name,
                product.product_name,
                price,
                1,
                0,
                'шт'
              ])
              results.products++
            } catch (err) {
              results.errors.push(`Product ${product.product_name}: ${err.message}`)
            }
          }
        }
        
        await connection.end()
        
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({
            success: true,
            message: 'Sync completed successfully',
            results: results
          })
        }
      } catch (error) {
        await connection.end()
        throw error
      }
    }

    // Get site config
    if ((path === '/site-config' || path === '/api/site-config' || path.endsWith('/site-config')) && method === 'GET') {
      try {
        const connection = await mysql.createConnection(dbConfig)
        const [configs] = await connection.execute('SELECT * FROM site_config')
        await connection.end()

        // Convert to key-value object
        const config = {}
        configs.forEach(item => {
          config[item.config_key] = item.config_value
        })

        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({
            success: true,
            config: config
          })
        }
      } catch (error) {
        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({
            success: true,
            config: {} // Return empty config if table doesn't exist
          })
        }
      }
    }

    // Update site config
    if ((path === '/site-config' || path === '/api/site-config' || path.endsWith('/site-config')) && method === 'POST') {
      try {
        const body = JSON.parse(event.body || '{}')
        const connection = await mysql.createConnection(dbConfig)

        // Update each config item
        for (const [key, value] of Object.entries(body)) {
          await connection.execute(`
            INSERT INTO site_config (config_key, config_value, updated_at)
            VALUES (?, ?, NOW())
            ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), updated_at = NOW()
          `, [key, value])
        }

        await connection.end()

        return {
          statusCode: 200,
          headers: corsHeaders,
          body: JSON.stringify({
            success: true,
            message: 'Site config updated successfully'
          })
        }
      } catch (error) {
        return {
          statusCode: 500,
          headers: corsHeaders,
          body: JSON.stringify({
            success: false,
            error: 'Failed to update config',
            message: error.message
          })
        }
      }
    }

    // Analytics placeholder
    if (path.includes('/analytics/event') && method === 'POST') {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({ success: true, message: 'Event logged' })
      }
    }

    // Default response
    return {
      statusCode: 404,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: 'Endpoint not found',
        path: path,
        method: method
      })
    }

  } catch (error) {
    console.error('API Error:', error)
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: 'Internal server error',
        message: error.message
      })
    }
  }
}
