// Simple branches endpoint that returns mock data for now
exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
    'Content-Type': 'application/json'
  }

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' }
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ success: false, error: 'Method not allowed' })
    }
  }

  try {
    // Return mock branches data for now
    const branches = [
      {
        id: 1,
        name: 'Main Branch',
        address: '123 Main St',
        phone: '+380123456789',
        is_active: 1
      },
      {
        id: 2,
        name: 'Downtown Branch',
        address: '456 Downtown Ave',
        phone: '+380987654321',
        is_active: 1
      }
    ]

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        branches: branches
      })
    }
  } catch (error) {
    console.error('Branches error:', error)
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Failed to fetch branches',
        message: error.message
      })
    }
  }
}
