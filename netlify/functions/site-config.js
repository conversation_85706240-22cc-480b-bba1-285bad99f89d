// Site config endpoint
import mysql from 'mysql2/promise'

const dbConfig = {
  host: 'avalon.cityhost.com.ua',
  port: 3306,
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos',
  charset: 'utf8mb4'
}

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
  'Content-Type': 'application/json'
}

export const handler = async (event, context) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' }
  }

  try {
    const connection = await mysql.createConnection(dbConfig)

    if (event.httpMethod === 'GET') {
      // Get site config
      try {
        const [configs] = await connection.execute('SELECT * FROM site_config')
        await connection.end()
        
        // Convert to key-value object
        const config = {}
        configs.forEach(item => {
          config[item.config_key] = item.config_value
        })
        
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            success: true,
            config: config
          })
        }
      } catch (error) {
        await connection.end()
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            success: true,
            config: {} // Return empty config if table doesn't exist
          })
        }
      }
    }

    if (event.httpMethod === 'POST') {
      // Update site config
      const body = JSON.parse(event.body || '{}')
      
      try {
        // Update each config item
        for (const [key, value] of Object.entries(body)) {
          await connection.execute(`
            INSERT INTO site_config (config_key, config_value, updated_at)
            VALUES (?, ?, NOW())
            ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), updated_at = NOW()
          `, [key, value])
        }
        
        await connection.end()
        
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            success: true,
            message: 'Site config updated successfully'
          })
        }
      } catch (error) {
        await connection.end()
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({
            success: false,
            error: 'Failed to update config',
            message: error.message
          })
        }
      }
    }

    await connection.end()
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ success: false, error: 'Method not allowed' })
    }

  } catch (error) {
    console.error('Site config error:', error)
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: 'Site config endpoint error',
        message: error.message
      })
    }
  }
}
