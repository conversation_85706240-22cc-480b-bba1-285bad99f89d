// Simple banners endpoint that returns mock data for now
exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
    'Content-Type': 'application/json'
  }

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' }
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ success: false, error: 'Method not allowed' })
    }
  }

  try {
    // Return mock banners data
    const banners = [
      {
        id: 1,
        title: 'Welcome to Our Store!',
        description: 'Fresh products delivered to your door',
        image_url: '/images/banner1.jpg',
        link_url: '/shop',
        is_active: 1,
        sort_order: 1
      },
      {
        id: 2,
        title: 'Special Offers',
        description: 'Get 20% off on your first order',
        image_url: '/images/banner2.jpg',
        link_url: '/shop?category=specials',
        is_active: 1,
        sort_order: 2
      }
    ]
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        banners: banners
      })
    }
  } catch (error) {
    console.error('Banners error:', error)
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        success: false, 
        error: 'Failed to fetch banners',
        message: error.message 
      })
    }
  }
}
