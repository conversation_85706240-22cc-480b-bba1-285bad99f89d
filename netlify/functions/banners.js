// Banners endpoint
import mysql from 'mysql2/promise'

const dbConfig = {
  host: 'avalon.cityhost.com.ua',
  port: 3306,
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos',
  charset: 'utf8mb4'
}

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
  'Content-Type': 'application/json'
}

export const handler = async (event, context) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' }
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ success: false, error: 'Method not allowed' })
    }
  }

  try {
    const connection = await mysql.createConnection(dbConfig)
    
    // Try to get banners, but handle if table doesn't exist
    let banners = []
    try {
      const [result] = await connection.execute('SELECT * FROM banners WHERE is_active = 1 ORDER BY sort_order')
      banners = result
    } catch (error) {
      console.log('Banners table not found, returning empty array')
    }
    
    await connection.end()
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        banners: banners
      })
    }
  } catch (error) {
    console.error('Banners error:', error)
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ 
        success: true, 
        banners: [] // Return empty array instead of error
      })
    }
  }
}
