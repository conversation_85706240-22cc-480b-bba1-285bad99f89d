// Simple analytics endpoint that accepts events
exports.handler = async (event, context) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
    'Content-Type': 'application/json'
  }

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' }
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ success: false, error: 'Method not allowed' })
    }
  }

  try {
    // Parse the analytics event data
    const eventData = JSON.parse(event.body || '{}')
    
    // Log the event (in a real implementation, you'd store this in a database)
    console.log('Analytics event received:', {
      type: eventData.type,
      category: eventData.category,
      action: eventData.action,
      label: eventData.label,
      value: eventData.value,
      timestamp: new Date().toISOString(),
      userAgent: event.headers['user-agent'],
      ip: event.headers['x-forwarded-for'] || event.headers['x-nf-client-connection-ip']
    })
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Event tracked successfully'
      })
    }
  } catch (error) {
    console.error('Analytics error:', error)
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        success: false, 
        error: 'Failed to track event',
        message: error.message 
      })
    }
  }
}
