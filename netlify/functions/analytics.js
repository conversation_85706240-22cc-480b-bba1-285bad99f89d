const express = require('express')
const cors = require('cors')
const serverless = require('serverless-http')

const app = express()

// CORS configuration
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization']
}))

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Track analytics event
app.post('/analytics/event', async (req, res) => {
  try {
    // For now, just log the event and return success
    console.log('Analytics event:', req.body)
    res.json({ success: true, message: 'Analytics event logged' })
  } catch (error) {
    console.error('Analytics event error:', error)
    res.status(500).json({ error: 'Failed to track event' })
  }
})

// Track user session
app.post('/analytics/session', async (req, res) => {
  try {
    // For now, just log the session and return success
    console.log('Analytics session:', req.body)
    res.json({ success: true, message: 'Session tracked' })
  } catch (error) {
    console.error('Session tracking error:', error)
    res.status(500).json({ error: 'Failed to track session' })
  }
})

// Get analytics dashboard data
app.get('/analytics/dashboard', async (req, res) => {
  try {
    const { timeframe = '7d' } = req.query
    
    // Return mock data for now
    res.json({
      success: true,
      data: {
        overview: {
          total_events: 0,
          total_sessions: 0,
          unique_users: 0,
          conversion_rate: 0
        },
        top_events: [],
        top_pages: [],
        device_breakdown: {},
        timeframe,
        date_range: {
          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString()
        }
      }
    })
  } catch (error) {
    console.error('Analytics dashboard error:', error)
    res.status(500).json({ error: 'Failed to get analytics data' })
  }
})

// Get real-time analytics
app.get('/analytics/realtime', async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        active_users: 0,
        recent_events: [],
        current_page_views: []
      }
    })
  } catch (error) {
    console.error('Real-time analytics error:', error)
    res.status(500).json({ error: 'Failed to get real-time data' })
  }
})

// Get e-commerce analytics
app.get('/analytics/ecommerce', async (req, res) => {
  try {
    const { timeframe = '7d' } = req.query
    
    res.json({
      success: true,
      data: {
        revenue: {
          total: 0,
          average_order_value: 0,
          total_orders: 0
        },
        top_products: [],
        conversion_funnel: {
          shop_views: 0,
          add_to_cart: 0,
          purchases: 0,
          view_to_cart_rate: 0,
          cart_to_purchase_rate: 0
        },
        timeframe
      }
    })
  } catch (error) {
    console.error('E-commerce analytics error:', error)
    res.status(500).json({ error: 'Failed to get e-commerce analytics' })
  }
})

// Export for Netlify Functions
module.exports.handler = serverless(app)
