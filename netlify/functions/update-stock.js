// Update stock quantities for all products
const mysql = require('mysql2/promise')

const dbConfig = {
  host: 'avalon.cityhost.com.ua',
  port: 3306,
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos',
  charset: 'utf8mb4'
}

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
  'Content-Type': 'application/json'
}

exports.handler = async (event, context) => {
  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    }
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    }
  }

  let connection
  try {
    console.log('🔄 Updating stock quantities...')

    // Database connection
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connected to database')

    // Update all products with zero stock to have default stock
    console.log('📦 Setting default stock for products with zero stock...')
    const [stockUpdateResult] = await connection.execute(`
      UPDATE products SET stock_quantity = 10 WHERE stock_quantity = 0
    `)
    
    console.log(`📦 Updated ${stockUpdateResult.affectedRows} products with default stock`)

    // Get final product counts by category
    const [categoryDist] = await connection.execute(`
      SELECT 
        c.name as category_name,
        COUNT(p.id) as product_count,
        SUM(CASE WHEN p.stock_quantity > 0 THEN 1 ELSE 0 END) as products_in_stock
      FROM categories c
      LEFT JOIN products p ON c.id = p.category_id
      GROUP BY c.id, c.name
      ORDER BY product_count DESC
    `)

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: `Successfully updated stock for ${stockUpdateResult.affectedRows} products`,
        categoryDistribution: categoryDist
      })
    }

  } catch (error) {
    console.error('❌ Error updating stock:', error)
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message,
        details: 'Stock update failed'
      })
    }
  } finally {
    if (connection) {
      try {
        await connection.end()
        console.log('🔌 Database connection closed')
      } catch (closeError) {
        console.error('Error closing connection:', closeError)
      }
    }
  }
}
