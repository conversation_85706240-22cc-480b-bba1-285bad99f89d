// Smart Product Categorization endpoint
import mysql from 'mysql2/promise'

const dbConfig = {
  host: 'avalon.cityhost.com.ua',
  port: 3306,
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos',
  charset: 'utf8mb4'
}

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
  'Content-Type': 'application/json'
}

export const handler = async (event, context) => {
  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    }
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    }
  }

  let connection
  try {
    console.log('🔄 Starting product categorization...')

    // Database connection using correct credentials
    connection = await mysql.createConnection(dbConfig)

    console.log('✅ Connected to database')

    // Execute the categorization updates step by step
    const updates = []

    // 1. Update weight-based snacks
    console.log('📝 Updating weight-based snacks...')
    const [result1] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Закуски Вагові' LIMIT 1)
      WHERE LOWER(name) LIKE '%ваг%' OR LOWER(name) LIKE '%кг%' OR LOWER(name) LIKE '%грам%'
    `)
    updates.push(`Weight-based snacks: ${result1.affectedRows} products`)

    // 2. Update piece-based snacks
    console.log('📝 Updating piece-based snacks...')
    const [result2] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Закуски штучні' LIMIT 1)
      WHERE LOWER(name) LIKE '%арахіс%' OR LOWER(name) LIKE '%сир%' OR LOWER(name) LIKE '%ікра%' OR LOWER(name) LIKE '%горіх%'
    `)
    updates.push(`Piece-based snacks: ${result2.affectedRows} products`)

    // 3. Update wine products
    console.log('📝 Updating wine products...')
    const [result3] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Вино' LIMIT 1)
      WHERE LOWER(name) LIKE '%вино%'
    `)
    updates.push(`Wine products: ${result3.affectedRows} products`)

    // 4. Update cider products
    console.log('📝 Updating cider products...')
    const [result4] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Сидр' LIMIT 1)
      WHERE LOWER(name) LIKE '%сидр%'
    `)
    updates.push(`Cider products: ${result4.affectedRows} products`)

    // 5. Update containers/packaging
    console.log('📝 Updating containers...')
    const [result5] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Тара' LIMIT 1)
      WHERE LOWER(name) LIKE '%тара%' OR LOWER(name) LIKE '%кришка%' OR LOWER(name) LIKE '%стаканчик%'
    `)
    updates.push(`Containers: ${result5.affectedRows} products`)

    // 6. Update draft beer
    console.log('📝 Updating draft beer...')
    const [result6] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Пиво розлив' LIMIT 1)
      WHERE LOWER(name) LIKE '%розлив%' OR LOWER(name) LIKE '%кег%'
    `)
    updates.push(`Draft beer: ${result6.affectedRows} products`)

    // 7. Update PET bottles
    console.log('📝 Updating PET bottles...')
    const [result7] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Пиво ПЕТ Опілля' LIMIT 1)
      WHERE LOWER(name) LIKE '%пет%' AND LOWER(name) LIKE '%кришка%'
    `)
    updates.push(`PET bottles: ${result7.affectedRows} products`)

    // 8. Update metal cans
    console.log('📝 Updating metal cans...')
    const [result8] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Пиво металева банка Опілля' LIMIT 1)
      WHERE LOWER(name) LIKE '%металева%' AND LOWER(name) LIKE '%банка%'
    `)
    updates.push(`Metal cans: ${result8.affectedRows} products`)

    // 9. Update imported cans
    console.log('📝 Updating imported cans...')
    const [result9] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'пиво ж/б імпорт' LIMIT 1)
      WHERE LOWER(name) LIKE '%жб%' OR LOWER(name) LIKE '%будвайзер%' OR LOWER(name) LIKE '%faxe%'
    `)
    updates.push(`Imported cans: ${result9.affectedRows} products`)

    // 10. Update Opillia glass bottles
    console.log('📝 Updating Opillia glass bottles...')
    const [result10] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Пиво Скло Опілля' LIMIT 1)
      WHERE LOWER(name) LIKE '%опілля%' AND category_id = 'cat_beverages_001'
    `)
    updates.push(`Opillia glass bottles: ${result10.affectedRows} products`)

    // 11. Update remaining beer products
    console.log('📝 Updating remaining beer products...')
    const [result11] = await connection.execute(`
      UPDATE products 
      SET category_id = (SELECT id FROM categories WHERE name = 'Пиво Скло Опілля' LIMIT 1)
      WHERE LOWER(name) LIKE '%пиво%' AND category_id = 'cat_beverages_001'
    `)
    updates.push(`Remaining beer products: ${result11.affectedRows} products`)

    // Get final category counts
    console.log('📊 Getting final category distribution...')
    const [finalCounts] = await connection.execute(`
      SELECT 
        c.name as category_name,
        COUNT(p.id) as product_count
      FROM categories c
      LEFT JOIN products p ON c.id = p.category_id
      GROUP BY c.id, c.name
      ORDER BY product_count DESC
    `)

    const totalUpdated = updates.reduce((sum, update) => {
      const match = update.match(/(\d+) products/)
      return sum + (match ? parseInt(match[1]) : 0)
    }, 0)

    console.log(`✅ Categorization completed! Updated ${totalUpdated} products`)

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: `Successfully categorized ${totalUpdated} products`,
        updates: updates,
        categoryDistribution: finalCounts
      })
    }

  } catch (error) {
    console.error('❌ Error during categorization:', error)
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message,
        details: 'Product categorization failed'
      })
    }
  } finally {
    if (connection) {
      try {
        await connection.end()
        console.log('🔌 Database connection closed')
      } catch (closeError) {
        console.error('Error closing connection:', closeError)
      }
    }
  }
}
