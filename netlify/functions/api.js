import express from 'express'
import serverless from 'serverless-http'
import cors from 'cors'
import mysql from 'mysql2/promise'

const app = express()

// Database configuration
const dbConfig = {
  host: 'avalon.cityhost.com.ua',
  port: 3306,
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos',
  charset: 'utf8mb4'
}

const POSTER_API_BASE = 'https://joinposter.com/api'
const POSTER_TOKEN = '218047:05891220e474bad7f26b6eaa0be3f344'

// Initialize database tables
const initializeTables = async () => {
  const connection = await mysql.createConnection(dbConfig)

  try {
    // Create banners table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS banners (
        id VARCHAR(36) PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        subtitle TEXT,
        image_url TEXT,
        link_url TEXT,
        link_text VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    // Create users table (for admin and customers)
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY,
        pos_client_id VARCHAR(255),
        email VARCHAR(255) UNIQUE,
        password_hash VARCHAR(255),
        name VARCHAR(255),
        phone VARCHAR(255),
        address JSON,
        latitude DECIMAL(10, 8),
        longitude DECIMAL(11, 8),
        role ENUM('admin', 'customer') DEFAULT 'customer',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    // Create carts table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS carts (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id)
      )
    `)

    // Create cart_items table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS cart_items (
        id VARCHAR(36) PRIMARY KEY,
        cart_id VARCHAR(36),
        product_id VARCHAR(255),
        quantity INT NOT NULL,
        price_at_add DECIMAL(10, 2),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_cart_id (cart_id),
        INDEX idx_product_id (product_id)
      )
    `)

    // Create orders table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS orders (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36),
        pos_order_id VARCHAR(255),
        branch_id VARCHAR(255),
        order_type ENUM('delivery', 'pickup') NOT NULL,
        status ENUM('pending', 'sent_to_pos', 'processing', 'ready_for_pickup', 'out_for_delivery', 'delivered', 'cancelled', 'failed') DEFAULT 'pending',
        subtotal DECIMAL(10, 2),
        delivery_fee DECIMAL(10, 2) DEFAULT 0,
        total_amount DECIMAL(10, 2),
        delivery_address JSON,
        delivery_latitude DECIMAL(10, 8),
        delivery_longitude DECIMAL(11, 8),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        pos_sent_at TIMESTAMP NULL,
        pos_last_status_update_at TIMESTAMP NULL,
        INDEX idx_user_id (user_id),
        INDEX idx_status (status)
      )
    `)

    // Create order_items table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS order_items (
        id VARCHAR(36) PRIMARY KEY,
        order_id VARCHAR(36),
        product_id VARCHAR(255),
        pos_product_id VARCHAR(255),
        name VARCHAR(255),
        quantity INT NOT NULL,
        unit_price DECIMAL(10, 2),
        total_price DECIMAL(10, 2),
        INDEX idx_order_id (order_id)
      )
    `)

    // Create notifications table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS notifications (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36),
        type ENUM('order_status', 'promo', 'system') DEFAULT 'system',
        title VARCHAR(255),
        message TEXT,
        order_id VARCHAR(36),
        read_status BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_read_status (read_status)
      )
    `)

    // Create site_config table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS site_config (
        id VARCHAR(36) PRIMARY KEY,
        key_name VARCHAR(255) UNIQUE NOT NULL,
        value JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)

    console.log('✅ Database tables initialized successfully')
  } catch (error) {
    console.error('❌ Error initializing database tables:', error)
  } finally {
    await connection.end()
  }
}

// Initialize tables on startup
initializeTables().catch(console.error)

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization']
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Generate simple ID
function generateId() {
  return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// Helper functions from working server implementation
function isWeightBasedProduct(product) {
  if (!product.attributes) return false
  try {
    const attrs = typeof product.attributes === 'string' ? JSON.parse(product.attributes) : product.attributes
    return attrs.ingredient_unit === 'kg'
  } catch {
    return false
  }
}

function needsPriceConversion(product) {
  return isWeightBasedProduct(product)
}

function convertPrice(price, product) {
  if (!needsPriceConversion(product)) return price
  // Convert from kg price to gram price (divide by 1000)
  return price / 1000
}

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'PWA POS Backend on Netlify Functions - Enhanced with Server Logic',
    timestamp: new Date().toISOString(),
    version: '1.1.0'
  })
})

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'PWA POS Shop Backend API on Netlify',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      sync: '/api/sync/full',
      categories: '/api/categories',
      products: '/api/products',
      branches: '/api/branches',
      banners: '/api/banners'
    }
  })
})

// ===== BANNER MANAGEMENT ENDPOINTS =====

// Get all banners
app.get('/api/banners', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [banners] = await connection.execute(
      'SELECT * FROM banners WHERE is_active = 1 ORDER BY sort_order, created_at DESC'
    )
    await connection.end()

    res.json({
      success: true,
      banners: banners
    })
  } catch (error) {
    console.error('Banners error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get single banner
app.get('/api/banners/:id', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [banners] = await connection.execute(
      'SELECT * FROM banners WHERE id = ?',
      [req.params.id]
    )
    await connection.end()

    if (banners.length === 0) {
      return res.status(404).json({ success: false, error: 'Banner not found' })
    }

    res.json({
      success: true,
      banner: banners[0]
    })
  } catch (error) {
    console.error('Banner error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Create banner
app.post('/api/banners', async (req, res) => {
  try {
    const { title, subtitle, image_url, link_url, link_text, is_active = true, sort_order = 0 } = req.body

    if (!title) {
      return res.status(400).json({ success: false, error: 'Title is required' })
    }

    const bannerId = generateId()
    const connection = await mysql.createConnection(dbConfig)

    await connection.execute(
      'INSERT INTO banners (id, title, subtitle, image_url, link_url, link_text, is_active, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [bannerId, title, subtitle, image_url, link_url, link_text, is_active, sort_order]
    )

    await connection.end()

    res.json({
      success: true,
      message: 'Banner created successfully',
      banner_id: bannerId
    })
  } catch (error) {
    console.error('Create banner error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Update banner
app.put('/api/banners/:id', async (req, res) => {
  try {
    const { title, subtitle, image_url, link_url, link_text, is_active, sort_order } = req.body
    const bannerId = req.params.id

    const connection = await mysql.createConnection(dbConfig)

    // Check if banner exists
    const [existing] = await connection.execute('SELECT id FROM banners WHERE id = ?', [bannerId])
    if (existing.length === 0) {
      await connection.end()
      return res.status(404).json({ success: false, error: 'Banner not found' })
    }

    await connection.execute(
      'UPDATE banners SET title = ?, subtitle = ?, image_url = ?, link_url = ?, link_text = ?, is_active = ?, sort_order = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [title, subtitle, image_url, link_url, link_text, is_active, sort_order, bannerId]
    )

    await connection.end()

    res.json({
      success: true,
      message: 'Banner updated successfully'
    })
  } catch (error) {
    console.error('Update banner error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Delete banner
app.delete('/api/banners/:id', async (req, res) => {
  try {
    const bannerId = req.params.id
    const connection = await mysql.createConnection(dbConfig)

    const [result] = await connection.execute('DELETE FROM banners WHERE id = ?', [bannerId])
    await connection.end()

    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, error: 'Banner not found' })
    }

    res.json({
      success: true,
      message: 'Banner deleted successfully'
    })
  } catch (error) {
    console.error('Delete banner error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get categories
app.get('/api/categories', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [categories] = await connection.execute('SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order')
    await connection.end()

    // Deduplicate categories by name, keeping the one with the most recent updated_at
    const categoryMap = new Map()
    categories.forEach(category => {
      const existing = categoryMap.get(category.name)
      if (!existing || new Date(category.updated_at) > new Date(existing.updated_at)) {
        categoryMap.set(category.name, category)
      }
    })

    const deduplicatedCategories = Array.from(categoryMap.values())
      .sort((a, b) => a.sort_order - b.sort_order || a.name.localeCompare(b.name))

    res.json(deduplicatedCategories)
  } catch (error) {
    console.error('Categories error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get branches
app.get('/api/branches', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [branches] = await connection.execute('SELECT * FROM branches WHERE is_active = 1 ORDER BY name')
    await connection.end()

    res.json({
      success: true,
      branches: branches
    })
  } catch (error) {
    console.error('Branches error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get orders
app.get('/api/orders', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [orders] = await connection.execute('SELECT * FROM orders ORDER BY created_at DESC LIMIT 100')
    await connection.end()

    res.json({
      success: true,
      orders: orders
    })
  } catch (error) {
    console.error('Orders error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get site config
app.get('/api/site-config', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [config] = await connection.execute('SELECT * FROM site_config WHERE is_active = 1')
    await connection.end()

    // Return default config if none found
    const siteConfig = config.length > 0 ? config[0] : {
      site_name: 'PosterPOS Shop',
      site_description: 'Modern PWA Shop with real-time inventory',
      contact_email: '<EMAIL>',
      contact_phone: '+380123456789',
      delivery_enabled: true,
      pickup_enabled: true,
      min_order_amount: 100
    }

    res.json({
      success: true,
      config: siteConfig
    })
  } catch (error) {
    console.error('Site config error:', error)
    // Return default config on error
    res.json({
      success: true,
      config: {
        site_name: 'PosterPOS Shop',
        site_description: 'Modern PWA Shop with real-time inventory',
        contact_email: '<EMAIL>',
        contact_phone: '+380123456789',
        delivery_enabled: true,
        pickup_enabled: true,
        min_order_amount: 100
      }
    })
  }
})

// Get banners
app.get('/api/banners', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [banners] = await connection.execute('SELECT * FROM banners WHERE is_active = 1 ORDER BY sort_order')
    await connection.end()

    res.json({
      success: true,
      data: banners
    })
  } catch (error) {
    console.error('Banners error:', error)
    // Return fallback data if database fails
    res.json({
      success: true,
      data: []
    })
  }
})

// Get products - Enhanced version based on working server implementation
app.get('/api/products', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)

    let query = `
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_active = 1
    `

    const params = []

    // Filter by category if provided
    if (req.query.categoryId || req.query.category_id) {
      const categoryId = req.query.categoryId || req.query.category_id
      query += ' AND p.category_id = ?'
      params.push(categoryId)
    }

    // Search by name if provided
    if (req.query.search) {
      query += ' AND p.name LIKE ?'
      params.push(`%${req.query.search}%`)
    }

    query += ' ORDER BY p.name'

    const [products] = await connection.execute(query, params)
    await connection.end()

    // Map database fields to frontend expected fields (based on working server implementation)
    const mappedProducts = products.map(product => {
      const stockQuantity = parseFloat(product.stock_quantity || 0)
      console.log(`Mapping product ${product.name}: stock_quantity=${product.stock_quantity}, parsed=${stockQuantity}`)

      // Create enhanced product object with inventory for weight-based detection
      const productWithInventory = {
        ...product,
        inventory: stockQuantity > 0 ? [{ quantity: stockQuantity }] : []
      }

      // Convert price for products that need conversion (weight-based or beverages with kg unit)
      const displayPrice = convertPrice(parseFloat(product.price || 0), productWithInventory)
      const displayOriginalPrice = product.original_price
        ? convertPrice(parseFloat(product.original_price), productWithInventory)
        : null

      return {
        id: product.id,
        poster_id: product.poster_id,
        poster_product_id: product.poster_id, // Alias for compatibility
        ingredient_id: product.ingredient_id,
        category_id: product.category_id,
        name: product.name,
        display_name: product.display_name || product.name,
        description: product.description || '',
        price: displayPrice,
        original_price: displayOriginalPrice,
        image_url: product.image_url || `https://via.placeholder.com/300x300/f3f4f6/6b7280?text=${encodeURIComponent(product.name.substring(0, 10))}`,
        display_image_url: product.display_image_url || product.image_url || `https://via.placeholder.com/300x300/f3f4f6/6b7280?text=${encodeURIComponent(product.name.substring(0, 10))}`,
        is_active: Boolean(product.is_active),
        stock_quantity: product.stock_quantity, // Keep original field
        min_quantity: product.min_quantity,
        unit: product.unit || 'шт',
        attributes: product.attributes,
        created_at: product.created_at,
        updated_at: product.updated_at,
        category_name: product.category_name,
        category: product.category_name ? { name: product.category_name } : null,

        // Frontend expected fields (mapped from database)
        quantity: stockQuantity, // Map stock_quantity to quantity
        available: Boolean(product.is_active) && stockQuantity > 0 // Calculate available
      }
    })

    // Return in the format expected by frontend
    res.json({
      success: true,
      products: mappedProducts
    })
  } catch (error) {
    console.error('Products error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get single product - Enhanced version based on working server implementation
app.get('/api/products/:id', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [products] = await connection.execute(`
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `, [req.params.id])
    await connection.end()

    if (products.length === 0) {
      return res.status(404).json({ success: false, error: 'Product not found' })
    }

    // Map database fields to frontend expected fields (based on working server implementation)
    const rawProduct = products[0]
    const stockQuantity = parseFloat(rawProduct.stock_quantity || 0)

    // Create enhanced product object with inventory for weight-based detection
    const productWithInventory = {
      ...rawProduct,
      inventory: stockQuantity > 0 ? [{ quantity: stockQuantity }] : []
    }

    // Convert price for products that need conversion (weight-based or beverages with kg unit)
    const displayPrice = convertPrice(parseFloat(rawProduct.price || 0), productWithInventory)
    const displayOriginalPrice = rawProduct.original_price
      ? convertPrice(parseFloat(rawProduct.original_price), productWithInventory)
      : null

    const product = {
      id: rawProduct.id,
      poster_id: rawProduct.poster_id,
      poster_product_id: rawProduct.poster_id, // Alias for compatibility
      ingredient_id: rawProduct.ingredient_id,
      category_id: rawProduct.category_id,
      name: rawProduct.name,
      display_name: rawProduct.display_name || rawProduct.name,
      description: rawProduct.description || '',
      price: displayPrice,
      original_price: displayOriginalPrice,
      image_url: rawProduct.image_url || `https://via.placeholder.com/300x300/f3f4f6/6b7280?text=${encodeURIComponent(rawProduct.name.substring(0, 10))}`,
      display_image_url: rawProduct.display_image_url || rawProduct.image_url || `https://via.placeholder.com/300x300/f3f4f6/6b7280?text=${encodeURIComponent(rawProduct.name.substring(0, 10))}`,
      is_active: Boolean(rawProduct.is_active),
      stock_quantity: rawProduct.stock_quantity, // Keep original field
      min_quantity: rawProduct.min_quantity,
      unit: rawProduct.unit || 'шт',
      attributes: rawProduct.attributes,
      created_at: rawProduct.created_at,
      updated_at: rawProduct.updated_at,
      category_name: rawProduct.category_name,
      category: rawProduct.category_name ? { name: rawProduct.category_name } : null,

      // Frontend expected fields (mapped from database)
      quantity: stockQuantity, // Map stock_quantity to quantity
      available: Boolean(rawProduct.is_active) && stockQuantity > 0 // Calculate available
    }

    res.json({
      success: true,
      product: product
    })
  } catch (error) {
    console.error('Product error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get branches
app.get('/api/branches', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [branches] = await connection.execute('SELECT * FROM branches WHERE is_active = 1 ORDER BY name')
    await connection.end()
    
    res.json({
      success: true,
      branches: branches
    })
  } catch (error) {
    console.error('Branches error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get banners (duplicate removed)

// Get site config
app.get('/api/site-config', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [configs] = await connection.execute('SELECT * FROM site_config')
    await connection.end()
    
    // Convert to key-value object
    const config = {}
    configs.forEach(item => {
      config[item.config_key] = item.config_value
    })
    
    res.json({
      success: true,
      config: config
    })
  } catch (error) {
    console.error('Site config error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Create order
app.post('/api/orders', async (req, res) => {
  try {
    const {
      customer_name,
      customer_phone,
      customer_email,
      delivery_type,
      delivery_address,
      branch_id,
      items,
      total_amount,
      notes
    } = req.body
    
    const connection = await mysql.createConnection(dbConfig)
    
    // Create order
    const orderId = generateId()
    await connection.execute(`
      INSERT INTO orders (
        id, customer_name, customer_phone, customer_email,
        delivery_type, delivery_address, branch_id, total_amount,
        notes, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      orderId, customer_name, customer_phone, customer_email,
      delivery_type, delivery_address, branch_id, total_amount,
      notes, 'pending'
    ])
    
    // Create order items
    for (const item of items) {
      await connection.execute(`
        INSERT INTO order_items (
          id, order_id, product_id, quantity, price, created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())
      `, [
        generateId(), orderId, item.product_id, item.quantity, item.price
      ])
    }
    
    await connection.end()
    
    res.json({
      success: true,
      order_id: orderId,
      message: 'Order created successfully'
    })
  } catch (error) {
    console.error('Order creation error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Sync endpoint
app.post('/api/sync/full', async (req, res) => {
  let connection = null
  
  try {
    console.log('🔄 Starting Netlify sync...')
    connection = await mysql.createConnection(dbConfig)
    
    let results = { categories: 0, products: 0, branches: 0, errors: [] }
    
    // Import fetch for Node.js
    const { default: fetch } = await import('node-fetch')
    
    // Sync categories
    console.log('📂 Syncing categories...')
    try {
      const categoriesResponse = await fetch(`${POSTER_API_BASE}/menu.getCategories?token=${POSTER_TOKEN}`, {
        timeout: 30000
      })
      const categoriesData = await categoriesResponse.json()
      
      if (categoriesData.response) {
        for (const cat of categoriesData.response) {
          try {
            const [existing] = await connection.execute(
              'SELECT id FROM categories WHERE poster_id = ?',
              [cat.category_id]
            )
            
            if (existing.length > 0) {
              await connection.execute(`
                UPDATE categories SET name = ?, description = ?, sort_order = ?, updated_at = NOW()
                WHERE poster_id = ?
              `, [
                cat.category_name,
                `Category: ${cat.category_name}`,
                parseInt(cat.sort_order) || 0,
                cat.category_id
              ])
            } else {
              await connection.execute(`
                INSERT INTO categories (id, poster_id, name, description, sort_order, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
              `, [
                generateId(),
                cat.category_id,
                cat.category_name,
                `Category: ${cat.category_name}`,
                parseInt(cat.sort_order) || 0,
                1
              ])
            }
            results.categories++
          } catch (err) {
            console.error('Category sync error:', err.message)
            results.errors.push(`Category ${cat.category_name}: ${err.message}`)
          }
        }
      }
    } catch (error) {
      console.error('Categories sync failed:', error.message)
      results.errors.push(`Categories sync: ${error.message}`)
    }
    
    // Sync products (continue in next part due to length limit)
    console.log('🛍️  Syncing products...')
    try {
      const productsResponse = await fetch(`${POSTER_API_BASE}/menu.getProducts?token=${POSTER_TOKEN}`, {
        timeout: 30000
      })
      const productsData = await productsResponse.json()
      
      if (productsData.response) {
        for (const product of productsData.response) {
          try {
            // Smart categorization based on product name
            let categoryId = null
            const productName = (product.product_name || '').toLowerCase()

            // First try to find by poster_id if available
            if (product.category_id) {
              const [categoryResult] = await connection.execute(
                'SELECT id FROM categories WHERE poster_id = ? LIMIT 1',
                [product.category_id]
              )

              if (categoryResult.length > 0) {
                categoryId = categoryResult[0].id
              }
            }

            // Smart categorization based on product name patterns
            if (!categoryId) {
              // Get all categories for pattern matching
              const [allCategories] = await connection.execute('SELECT id, name, poster_id FROM categories')

              // Define categorization patterns
              const patterns = {
                'Пиво Скло Опілля': ['опілля', 'скло', 'пляшка'],
                'Пиво розлив': ['розлив', 'draft', 'кег'],
                'Пиво металева банка Опілля': ['банка', 'металева', 'опілля'],
                'Пиво ПЕТ Опілля': ['пет', 'пластик', 'опілля'],
                'пиво ж/б імпорт': ['імпорт', 'import', 'жб'],
                'Вино': ['вино', 'wine', 'червоне', 'біле'],
                'Сидр': ['сидр', 'cider'],
                'Закуски Вагові': ['ваг', 'вагов', 'кг', 'грам'],
                'Закуски штучні': ['штучн', 'шт', 'piece'],
                'Напої б/а': ['кола', 'пепсі', 'фанта', 'спрайт', 'сік', 'вода'],
                'Тара': ['тара', 'пляшка порожня', 'кег порожній'],
                'Інші': ['інш', 'other', 'різне']
              }

              // Try to match product name to category patterns
              for (const [categoryName, keywords] of Object.entries(patterns)) {
                if (keywords.some(keyword => productName.includes(keyword))) {
                  const category = allCategories.find(cat => cat.name === categoryName)
                  if (category) {
                    categoryId = category.id
                    break
                  }
                }
              }

              // If still no category, try beer-related patterns
              if (!categoryId && (productName.includes('пиво') || productName.includes('beer'))) {
                const beerCategory = allCategories.find(cat => cat.name === 'Пиво Скло Опілля')
                if (beerCategory) categoryId = beerCategory.id
              }

              // Default to snacks for food items
              if (!categoryId && (productName.includes('сир') || productName.includes('м\'ясо') ||
                                 productName.includes('риба') || productName.includes('ікра') ||
                                 productName.includes('арахіс') || productName.includes('горіх'))) {
                const snacksCategory = allCategories.find(cat => cat.name === 'Закуски штучні')
                if (snacksCategory) categoryId = snacksCategory.id
              }

              // Final fallback to general beverages
              if (!categoryId) {
                const beverageCategory = allCategories.find(cat => cat.name === 'Напої')
                if (beverageCategory) categoryId = beverageCategory.id
              }
            }
            
            if (!categoryId) continue
            
            // Calculate price
            let price = 0
            if (product.price) {
              if (typeof product.price === 'object') {
                price = parseFloat(product.price['1'] || product.price['0'] || 0) / 100
              } else {
                price = parseFloat(product.price) / 100
              }
            }
            
            const [existing] = await connection.execute(
              'SELECT id FROM products WHERE poster_id = ?',
              [product.product_id]
            )
            
            if (existing.length > 0) {
              await connection.execute(`
                UPDATE products SET name = ?, description = ?, price = ?, category_id = ?, updated_at = NOW()
                WHERE poster_id = ?
              `, [
                product.product_name,
                product.product_name,
                price,
                categoryId,
                product.product_id
              ])
            } else {
              await connection.execute(`
                INSERT INTO products (id, poster_id, category_id, name, description, price, is_active, stock_quantity, unit, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
              `, [
                generateId(),
                product.product_id,
                categoryId,
                product.product_name,
                product.product_name,
                price,
                1,
                0,
                'шт'
              ])
            }
            results.products++
          } catch (err) {
            console.error('Product sync error:', err.message)
            results.errors.push(`Product ${product.product_name}: ${err.message}`)
          }
        }
      }
    } catch (error) {
      console.error('Products sync failed:', error.message)
      results.errors.push(`Products sync: ${error.message}`)
    }
    
    console.log(`✅ Netlify sync completed: ${results.categories} categories, ${results.products} products`)
    
    res.json({
      success: true,
      message: 'Netlify sync completed successfully',
      results: results
    })
    
  } catch (error) {
    console.error('Netlify sync error:', error)
    res.status(500).json({
      success: false,
      error: error.message
    })
  } finally {
    if (connection) {
      await connection.end()
    }
  }
})

// Orders endpoint
app.get('/api/orders', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [orders] = await connection.execute(`
      SELECT * FROM orders
      WHERE is_active = 1
      ORDER BY created_at DESC
      LIMIT 50
    `)
    await connection.end()

    res.json({
      success: true,
      orders: orders
    })
  } catch (error) {
    console.error('Orders error:', error)
    // Return fallback data if database fails
    res.json({
      success: true,
      orders: []
    })
  }
})

// ===== CART MANAGEMENT ENDPOINTS =====

// Get user cart
app.get('/api/cart/:userId', async (req, res) => {
  try {
    const { userId } = req.params
    const connection = await mysql.createConnection(dbConfig)

    // Get or create cart for user
    let [carts] = await connection.execute('SELECT * FROM carts WHERE user_id = ?', [userId])

    if (carts.length === 0) {
      const cartId = generateId()
      await connection.execute(
        'INSERT INTO carts (id, user_id) VALUES (?, ?)',
        [cartId, userId]
      )
      carts = [{ id: cartId, user_id: userId, created_at: new Date(), updated_at: new Date() }]
    }

    const cart = carts[0]

    // Get cart items with product details
    const [cartItems] = await connection.execute(`
      SELECT ci.*, p.name, p.price, p.image_url, p.unit
      FROM cart_items ci
      LEFT JOIN products p ON ci.product_id = p.id
      WHERE ci.cart_id = ?
      ORDER BY ci.created_at DESC
    `, [cart.id])

    await connection.end()

    // Calculate totals
    const subtotal = cartItems.reduce((sum, item) => sum + (item.price_at_add * item.quantity), 0)
    const itemCount = cartItems.reduce((sum, item) => sum + item.quantity, 0)

    res.json({
      success: true,
      cart: {
        id: cart.id,
        user_id: cart.user_id,
        items: cartItems,
        subtotal: subtotal,
        item_count: itemCount,
        created_at: cart.created_at,
        updated_at: cart.updated_at
      }
    })
  } catch (error) {
    console.error('Get cart error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Add item to cart
app.post('/api/cart/:userId/items', async (req, res) => {
  try {
    const { userId } = req.params
    const { product_id, quantity = 1 } = req.body

    if (!product_id) {
      return res.status(400).json({ success: false, error: 'Product ID is required' })
    }

    const connection = await mysql.createConnection(dbConfig)

    // Get or create cart
    let [carts] = await connection.execute('SELECT * FROM carts WHERE user_id = ?', [userId])

    if (carts.length === 0) {
      const cartId = generateId()
      await connection.execute(
        'INSERT INTO carts (id, user_id) VALUES (?, ?)',
        [cartId, userId]
      )
      carts = [{ id: cartId, user_id: userId }]
    }

    const cart = carts[0]

    // Get product details
    const [products] = await connection.execute('SELECT * FROM products WHERE id = ?', [product_id])
    if (products.length === 0) {
      await connection.end()
      return res.status(404).json({ success: false, error: 'Product not found' })
    }

    const product = products[0]

    // Check if item already exists in cart
    const [existingItems] = await connection.execute(
      'SELECT * FROM cart_items WHERE cart_id = ? AND product_id = ?',
      [cart.id, product_id]
    )

    if (existingItems.length > 0) {
      // Update quantity
      await connection.execute(
        'UPDATE cart_items SET quantity = quantity + ?, updated_at = CURRENT_TIMESTAMP WHERE cart_id = ? AND product_id = ?',
        [quantity, cart.id, product_id]
      )
    } else {
      // Add new item
      const itemId = generateId()
      await connection.execute(
        'INSERT INTO cart_items (id, cart_id, product_id, quantity, price_at_add) VALUES (?, ?, ?, ?, ?)',
        [itemId, cart.id, product_id, quantity, product.price]
      )
    }

    // Update cart timestamp
    await connection.execute(
      'UPDATE carts SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [cart.id]
    )

    await connection.end()

    res.json({
      success: true,
      message: 'Item added to cart successfully'
    })
  } catch (error) {
    console.error('Add to cart error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Update cart item quantity
app.put('/api/cart/:userId/items/:itemId', async (req, res) => {
  try {
    const { userId, itemId } = req.params
    const { quantity } = req.body

    if (quantity < 0) {
      return res.status(400).json({ success: false, error: 'Quantity cannot be negative' })
    }

    const connection = await mysql.createConnection(dbConfig)

    if (quantity === 0) {
      // Remove item
      await connection.execute('DELETE FROM cart_items WHERE id = ?', [itemId])
    } else {
      // Update quantity
      await connection.execute(
        'UPDATE cart_items SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [quantity, itemId]
      )
    }

    await connection.end()

    res.json({
      success: true,
      message: quantity === 0 ? 'Item removed from cart' : 'Cart item updated successfully'
    })
  } catch (error) {
    console.error('Update cart item error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Remove item from cart
app.delete('/api/cart/:userId/items/:itemId', async (req, res) => {
  try {
    const { itemId } = req.params
    const connection = await mysql.createConnection(dbConfig)

    const [result] = await connection.execute('DELETE FROM cart_items WHERE id = ?', [itemId])
    await connection.end()

    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, error: 'Cart item not found' })
    }

    res.json({
      success: true,
      message: 'Item removed from cart successfully'
    })
  } catch (error) {
    console.error('Remove cart item error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Clear cart
app.delete('/api/cart/:userId', async (req, res) => {
  try {
    const { userId } = req.params
    const connection = await mysql.createConnection(dbConfig)

    // Get user's cart
    const [carts] = await connection.execute('SELECT * FROM carts WHERE user_id = ?', [userId])

    if (carts.length > 0) {
      // Delete all cart items
      await connection.execute('DELETE FROM cart_items WHERE cart_id = ?', [carts[0].id])
    }

    await connection.end()

    res.json({
      success: true,
      message: 'Cart cleared successfully'
    })
  } catch (error) {
    console.error('Clear cart error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Product inventory endpoint
app.get('/api/products/:productId/inventory/:branchId', async (req, res) => {
  try {
    const { productId, branchId } = req.params
    const connection = await mysql.createConnection(dbConfig)

    // Get inventory for specific product and branch
    const [inventory] = await connection.execute(
      'SELECT * FROM inventory WHERE product_id = ? AND branch_id = ?',
      [productId, branchId]
    )

    await connection.end()

    if (inventory.length > 0) {
      res.json({
        success: true,
        inventory: inventory[0]
      })
    } else {
      // Return default inventory if not found
      res.json({
        success: true,
        inventory: {
          product_id: productId,
          branch_id: branchId,
          quantity: 0,
          available: false
        }
      })
    }
  } catch (error) {
    console.error('Inventory error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Site config PUT endpoint
app.put('/api/site-config', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const config = req.body

    // Update site configuration in database
    // For now, just return success as we don't have a site_config table
    // In a real implementation, you would update the database

    await connection.end()

    res.json({
      success: true,
      message: 'Site configuration updated successfully',
      config: config
    })
  } catch (error) {
    console.error('Site config update error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// ===== USER MANAGEMENT & AUTHENTICATION ENDPOINTS =====

// Admin login
app.post('/api/auth/admin/login', async (req, res) => {
  try {
    const { email, password } = req.body

    if (!email || !password) {
      return res.status(400).json({ success: false, error: 'Email and password are required' })
    }

    // Simple admin check - in production, use proper password hashing
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>'
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'

    if (email === adminEmail && password === adminPassword) {
      // Generate simple token (in production, use JWT)
      const token = Buffer.from(`${email}:${Date.now()}`).toString('base64')

      res.json({
        success: true,
        message: 'Admin login successful',
        token: token,
        user: {
          id: 'admin',
          email: email,
          role: 'admin',
          name: 'Administrator'
        }
      })
    } else {
      res.status(401).json({ success: false, error: 'Invalid credentials' })
    }
  } catch (error) {
    console.error('Admin login error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Create customer user
app.post('/api/users', async (req, res) => {
  try {
    const {
      email,
      name,
      phone,
      address,
      latitude,
      longitude,
      pos_client_id
    } = req.body

    if (!email || !name) {
      return res.status(400).json({ success: false, error: 'Email and name are required' })
    }

    const connection = await mysql.createConnection(dbConfig)

    // Check if user already exists
    const [existing] = await connection.execute('SELECT id FROM users WHERE email = ?', [email])

    if (existing.length > 0) {
      await connection.end()
      return res.status(400).json({ success: false, error: 'User with this email already exists' })
    }

    const userId = generateId()
    await connection.execute(`
      INSERT INTO users (
        id, pos_client_id, email, name, phone, address, latitude, longitude, role
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'customer')
    `, [
      userId, pos_client_id, email, name, phone,
      JSON.stringify(address), latitude, longitude
    ])

    await connection.end()

    res.json({
      success: true,
      message: 'User created successfully',
      user_id: userId
    })
  } catch (error) {
    console.error('Create user error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get user by ID
app.get('/api/users/:userId', async (req, res) => {
  try {
    const { userId } = req.params
    const connection = await mysql.createConnection(dbConfig)

    const [users] = await connection.execute('SELECT * FROM users WHERE id = ?', [userId])
    await connection.end()

    if (users.length === 0) {
      return res.status(404).json({ success: false, error: 'User not found' })
    }

    const user = users[0]

    // Parse address if it exists
    if (user.address) {
      try {
        user.address = JSON.parse(user.address)
      } catch (e) {
        // Keep as string if parsing fails
      }
    }

    // Remove sensitive data
    delete user.password_hash

    res.json({
      success: true,
      user: user
    })
  } catch (error) {
    console.error('Get user error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Update user
app.put('/api/users/:userId', async (req, res) => {
  try {
    const { userId } = req.params
    const { name, phone, address, latitude, longitude } = req.body

    const connection = await mysql.createConnection(dbConfig)

    const [result] = await connection.execute(`
      UPDATE users SET
        name = ?, phone = ?, address = ?, latitude = ?, longitude = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [name, phone, JSON.stringify(address), latitude, longitude, userId])

    await connection.end()

    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, error: 'User not found' })
    }

    res.json({
      success: true,
      message: 'User updated successfully'
    })
  } catch (error) {
    console.error('Update user error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// ===== ORDER MANAGEMENT ENDPOINTS =====

// Create order from cart
app.post('/api/orders', async (req, res) => {
  try {
    const {
      user_id,
      branch_id,
      order_type,
      delivery_address,
      delivery_latitude,
      delivery_longitude,
      notes
    } = req.body

    if (!user_id || !branch_id || !order_type) {
      return res.status(400).json({
        success: false,
        error: 'User ID, branch ID, and order type are required'
      })
    }

    const connection = await mysql.createConnection(dbConfig)

    // Get user's cart
    const [carts] = await connection.execute('SELECT * FROM carts WHERE user_id = ?', [user_id])

    if (carts.length === 0) {
      await connection.end()
      return res.status(400).json({ success: false, error: 'Cart not found' })
    }

    const cart = carts[0]

    // Get cart items with product details
    const [cartItems] = await connection.execute(`
      SELECT ci.*, p.name, p.price
      FROM cart_items ci
      LEFT JOIN products p ON ci.product_id = p.id
      WHERE ci.cart_id = ?
    `, [cart.id])

    if (cartItems.length === 0) {
      await connection.end()
      return res.status(400).json({ success: false, error: 'Cart is empty' })
    }

    // Calculate totals
    const subtotal = cartItems.reduce((sum, item) => sum + (item.price_at_add * item.quantity), 0)
    const deliveryFee = order_type === 'delivery' ? 50 : 0 // 50 UAH delivery fee
    const totalAmount = subtotal + deliveryFee

    // Create order
    const orderId = generateId()
    await connection.execute(`
      INSERT INTO orders (
        id, user_id, branch_id, order_type, status, subtotal, delivery_fee, total_amount,
        delivery_address, delivery_latitude, delivery_longitude, notes
      ) VALUES (?, ?, ?, ?, 'pending', ?, ?, ?, ?, ?, ?, ?)
    `, [
      orderId, user_id, branch_id, order_type, subtotal, deliveryFee, totalAmount,
      delivery_address ? JSON.stringify(delivery_address) : null,
      delivery_latitude || null,
      delivery_longitude || null,
      notes || null
    ])

    // Create order items
    for (const item of cartItems) {
      const orderItemId = generateId()
      await connection.execute(`
        INSERT INTO order_items (
          id, order_id, product_id, name, quantity, unit_price, total_price
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        orderItemId, orderId, item.product_id, item.name || 'Unknown Product',
        item.quantity, item.price_at_add || item.price || 0,
        (item.price_at_add || item.price || 0) * item.quantity
      ])
    }

    // Clear cart
    await connection.execute('DELETE FROM cart_items WHERE cart_id = ?', [cart.id])

    await connection.end()

    res.json({
      success: true,
      message: 'Order created successfully',
      order_id: orderId,
      total_amount: totalAmount
    })
  } catch (error) {
    console.error('Create order error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get user orders
app.get('/api/orders/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params
    const connection = await mysql.createConnection(dbConfig)

    const [orders] = await connection.execute(`
      SELECT o.*, b.name as branch_name
      FROM orders o
      LEFT JOIN branches b ON o.branch_id = b.id
      WHERE o.user_id = ?
      ORDER BY o.created_at DESC
    `, [userId])

    // Get order items for each order
    for (const order of orders) {
      const [items] = await connection.execute(
        'SELECT * FROM order_items WHERE order_id = ?',
        [order.id]
      )
      order.items = items

      // Parse delivery address if it exists
      if (order.delivery_address) {
        try {
          order.delivery_address = JSON.parse(order.delivery_address)
        } catch (e) {
          // Keep as string if parsing fails
        }
      }
    }

    await connection.end()

    res.json({
      success: true,
      orders: orders
    })
  } catch (error) {
    console.error('Get user orders error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get single order
app.get('/api/orders/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params
    const connection = await mysql.createConnection(dbConfig)

    const [orders] = await connection.execute(`
      SELECT o.*, b.name as branch_name, u.name as user_name, u.phone as user_phone
      FROM orders o
      LEFT JOIN branches b ON o.branch_id = b.id
      LEFT JOIN users u ON o.user_id = u.id
      WHERE o.id = ?
    `, [orderId])

    if (orders.length === 0) {
      await connection.end()
      return res.status(404).json({ success: false, error: 'Order not found' })
    }

    const order = orders[0]

    // Get order items
    const [items] = await connection.execute(
      'SELECT * FROM order_items WHERE order_id = ?',
      [orderId]
    )
    order.items = items

    // Parse delivery address if it exists
    if (order.delivery_address) {
      try {
        order.delivery_address = JSON.parse(order.delivery_address)
      } catch (e) {
        // Keep as string if parsing fails
      }
    }

    await connection.end()

    res.json({
      success: true,
      order: order
    })
  } catch (error) {
    console.error('Get order error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Update order status
app.put('/api/orders/:orderId/status', async (req, res) => {
  try {
    const { orderId } = req.params
    const { status } = req.body

    const validStatuses = [
      'pending', 'sent_to_pos', 'processing', 'ready_for_pickup',
      'out_for_delivery', 'delivered', 'cancelled', 'failed'
    ]

    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status. Valid statuses: ' + validStatuses.join(', ')
      })
    }

    const connection = await mysql.createConnection(dbConfig)

    const [result] = await connection.execute(
      'UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, orderId]
    )

    await connection.end()

    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, error: 'Order not found' })
    }

    res.json({
      success: true,
      message: 'Order status updated successfully'
    })
  } catch (error) {
    console.error('Update order status error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Analytics endpoints (placeholder)
app.post('/api/analytics/event', (req, res) => {
  console.log('📊 Analytics event:', req.body.event || 'unknown')
  res.json({ success: true, message: 'Event logged' })
})

app.post('/api/analytics/session', (req, res) => {
  console.log('📊 Analytics session:', req.body.session_id || 'unknown')
  res.json({ success: true, message: 'Session logged' })
})

app.get('/api/analytics/dashboard', (req, res) => {
  res.json({
    success: true,
    data: {
      overview: { total_events: 0, total_sessions: 0, unique_users: 0, conversion_rate: 0 },
      top_events: [],
      top_pages: [],
      device_breakdown: {},
      timeframe: req.query.timeframe || '7d'
    }
  })
})

// Error handling
app.use((err, req, res, next) => {
  console.error('API Error:', err)
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.originalUrl
  })
})

// Export handler for Netlify Functions
export const handler = serverless(app)
