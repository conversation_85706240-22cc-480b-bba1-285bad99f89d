import express from 'express'
import serverless from 'serverless-http'
import cors from 'cors'
import mysql from 'mysql2/promise'

const app = express()

// Database configuration
const dbConfig = {
  host: 'avalon.cityhost.com.ua',
  port: 3306,
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos',
  charset: 'utf8mb4'
}

const POSTER_API_BASE = 'https://joinposter.com/api'
const POSTER_TOKEN = '218047:05891220e474bad7f26b6eaa0be3f344'

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization']
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Generate simple ID
function generateId() {
  return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// Helper functions from working server implementation
function isWeightBasedProduct(product) {
  if (!product.attributes) return false
  try {
    const attrs = typeof product.attributes === 'string' ? JSON.parse(product.attributes) : product.attributes
    return attrs.ingredient_unit === 'kg'
  } catch {
    return false
  }
}

function needsPriceConversion(product) {
  return isWeightBasedProduct(product)
}

function convertPrice(price, product) {
  if (!needsPriceConversion(product)) return price
  // Convert from kg price to gram price (divide by 1000)
  return price / 1000
}

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'PWA POS Backend on Netlify Functions - Enhanced with Server Logic',
    timestamp: new Date().toISOString(),
    version: '1.1.0'
  })
})

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'PWA POS Shop Backend API on Netlify',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      sync: '/api/sync/full',
      categories: '/api/categories',
      products: '/api/products',
      branches: '/api/branches',
      banners: '/api/banners'
    }
  })
})

// Get categories
app.get('/api/categories', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [categories] = await connection.execute('SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order')
    await connection.end()

    res.json({
      success: true,
      categories: categories
    })
  } catch (error) {
    console.error('Categories error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get branches
app.get('/api/branches', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [branches] = await connection.execute('SELECT * FROM branches WHERE is_active = 1 ORDER BY name')
    await connection.end()

    res.json({
      success: true,
      branches: branches
    })
  } catch (error) {
    console.error('Branches error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get orders
app.get('/api/orders', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [orders] = await connection.execute('SELECT * FROM orders ORDER BY created_at DESC LIMIT 100')
    await connection.end()

    res.json({
      success: true,
      orders: orders
    })
  } catch (error) {
    console.error('Orders error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get site config
app.get('/api/site-config', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [config] = await connection.execute('SELECT * FROM site_config WHERE is_active = 1')
    await connection.end()

    // Return default config if none found
    const siteConfig = config.length > 0 ? config[0] : {
      site_name: 'PosterPOS Shop',
      site_description: 'Modern PWA Shop with real-time inventory',
      contact_email: '<EMAIL>',
      contact_phone: '+380123456789',
      delivery_enabled: true,
      pickup_enabled: true,
      min_order_amount: 100
    }

    res.json({
      success: true,
      config: siteConfig
    })
  } catch (error) {
    console.error('Site config error:', error)
    // Return default config on error
    res.json({
      success: true,
      config: {
        site_name: 'PosterPOS Shop',
        site_description: 'Modern PWA Shop with real-time inventory',
        contact_email: '<EMAIL>',
        contact_phone: '+380123456789',
        delivery_enabled: true,
        pickup_enabled: true,
        min_order_amount: 100
      }
    })
  }
})

// Get banners
app.get('/api/banners', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [banners] = await connection.execute('SELECT * FROM banners WHERE is_active = 1 ORDER BY sort_order')
    await connection.end()

    res.json({
      success: true,
      banners: banners
    })
  } catch (error) {
    console.error('Banners error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get products - Enhanced version based on working server implementation
app.get('/api/products', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)

    let query = `
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_active = 1
    `

    const params = []

    // Filter by category if provided
    if (req.query.categoryId || req.query.category_id) {
      const categoryId = req.query.categoryId || req.query.category_id
      query += ' AND p.category_id = ?'
      params.push(categoryId)
    }

    // Search by name if provided
    if (req.query.search) {
      query += ' AND p.name LIKE ?'
      params.push(`%${req.query.search}%`)
    }

    query += ' ORDER BY p.name'

    const [products] = await connection.execute(query, params)
    await connection.end()

    // Map database fields to frontend expected fields (based on working server implementation)
    const mappedProducts = products.map(product => {
      const stockQuantity = parseFloat(product.stock_quantity || 0)
      console.log(`Mapping product ${product.name}: stock_quantity=${product.stock_quantity}, parsed=${stockQuantity}`)

      // Create enhanced product object with inventory for weight-based detection
      const productWithInventory = {
        ...product,
        inventory: stockQuantity > 0 ? [{ quantity: stockQuantity }] : []
      }

      // Convert price for products that need conversion (weight-based or beverages with kg unit)
      const displayPrice = convertPrice(parseFloat(product.price || 0), productWithInventory)
      const displayOriginalPrice = product.original_price
        ? convertPrice(parseFloat(product.original_price), productWithInventory)
        : null

      return {
        id: product.id,
        poster_id: product.poster_id,
        poster_product_id: product.poster_id, // Alias for compatibility
        ingredient_id: product.ingredient_id,
        category_id: product.category_id,
        name: product.name,
        display_name: product.display_name || product.name,
        description: product.description || '',
        price: displayPrice,
        original_price: displayOriginalPrice,
        image_url: product.image_url || '',
        display_image_url: product.display_image_url || product.image_url || '',
        is_active: Boolean(product.is_active),
        stock_quantity: product.stock_quantity, // Keep original field
        min_quantity: product.min_quantity,
        unit: product.unit || 'шт',
        attributes: product.attributes,
        created_at: product.created_at,
        updated_at: product.updated_at,
        category_name: product.category_name,
        category: product.category_name ? { name: product.category_name } : null,

        // Frontend expected fields (mapped from database)
        quantity: stockQuantity, // Map stock_quantity to quantity
        available: Boolean(product.is_active) && stockQuantity > 0 // Calculate available
      }
    })

    // Return in the format expected by frontend
    res.json({
      success: true,
      products: mappedProducts
    })
  } catch (error) {
    console.error('Products error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get single product - Enhanced version based on working server implementation
app.get('/api/products/:id', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [products] = await connection.execute(`
      SELECT p.*, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `, [req.params.id])
    await connection.end()

    if (products.length === 0) {
      return res.status(404).json({ success: false, error: 'Product not found' })
    }

    // Map database fields to frontend expected fields (based on working server implementation)
    const rawProduct = products[0]
    const stockQuantity = parseFloat(rawProduct.stock_quantity || 0)

    // Create enhanced product object with inventory for weight-based detection
    const productWithInventory = {
      ...rawProduct,
      inventory: stockQuantity > 0 ? [{ quantity: stockQuantity }] : []
    }

    // Convert price for products that need conversion (weight-based or beverages with kg unit)
    const displayPrice = convertPrice(parseFloat(rawProduct.price || 0), productWithInventory)
    const displayOriginalPrice = rawProduct.original_price
      ? convertPrice(parseFloat(rawProduct.original_price), productWithInventory)
      : null

    const product = {
      id: rawProduct.id,
      poster_id: rawProduct.poster_id,
      poster_product_id: rawProduct.poster_id, // Alias for compatibility
      ingredient_id: rawProduct.ingredient_id,
      category_id: rawProduct.category_id,
      name: rawProduct.name,
      display_name: rawProduct.display_name || rawProduct.name,
      description: rawProduct.description || '',
      price: displayPrice,
      original_price: displayOriginalPrice,
      image_url: rawProduct.image_url || '',
      display_image_url: rawProduct.display_image_url || rawProduct.image_url || '',
      is_active: Boolean(rawProduct.is_active),
      stock_quantity: rawProduct.stock_quantity, // Keep original field
      min_quantity: rawProduct.min_quantity,
      unit: rawProduct.unit || 'шт',
      attributes: rawProduct.attributes,
      created_at: rawProduct.created_at,
      updated_at: rawProduct.updated_at,
      category_name: rawProduct.category_name,
      category: rawProduct.category_name ? { name: rawProduct.category_name } : null,

      // Frontend expected fields (mapped from database)
      quantity: stockQuantity, // Map stock_quantity to quantity
      available: Boolean(rawProduct.is_active) && stockQuantity > 0 // Calculate available
    }

    res.json({
      success: true,
      product: product
    })
  } catch (error) {
    console.error('Product error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get branches
app.get('/api/branches', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [branches] = await connection.execute('SELECT * FROM branches WHERE is_active = 1 ORDER BY name')
    await connection.end()
    
    res.json({
      success: true,
      branches: branches
    })
  } catch (error) {
    console.error('Branches error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get banners
app.get('/api/banners', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [banners] = await connection.execute('SELECT * FROM banners WHERE is_active = 1 ORDER BY sort_order')
    await connection.end()
    
    res.json({
      success: true,
      banners: banners
    })
  } catch (error) {
    console.error('Banners error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Get site config
app.get('/api/site-config', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig)
    const [configs] = await connection.execute('SELECT * FROM site_config')
    await connection.end()
    
    // Convert to key-value object
    const config = {}
    configs.forEach(item => {
      config[item.config_key] = item.config_value
    })
    
    res.json({
      success: true,
      config: config
    })
  } catch (error) {
    console.error('Site config error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Create order
app.post('/api/orders', async (req, res) => {
  try {
    const {
      customer_name,
      customer_phone,
      customer_email,
      delivery_type,
      delivery_address,
      branch_id,
      items,
      total_amount,
      notes
    } = req.body
    
    const connection = await mysql.createConnection(dbConfig)
    
    // Create order
    const orderId = generateId()
    await connection.execute(`
      INSERT INTO orders (
        id, customer_name, customer_phone, customer_email,
        delivery_type, delivery_address, branch_id, total_amount,
        notes, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      orderId, customer_name, customer_phone, customer_email,
      delivery_type, delivery_address, branch_id, total_amount,
      notes, 'pending'
    ])
    
    // Create order items
    for (const item of items) {
      await connection.execute(`
        INSERT INTO order_items (
          id, order_id, product_id, quantity, price, created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())
      `, [
        generateId(), orderId, item.product_id, item.quantity, item.price
      ])
    }
    
    await connection.end()
    
    res.json({
      success: true,
      order_id: orderId,
      message: 'Order created successfully'
    })
  } catch (error) {
    console.error('Order creation error:', error)
    res.status(500).json({ success: false, error: error.message })
  }
})

// Sync endpoint
app.post('/api/sync/full', async (req, res) => {
  let connection = null
  
  try {
    console.log('🔄 Starting Netlify sync...')
    connection = await mysql.createConnection(dbConfig)
    
    let results = { categories: 0, products: 0, branches: 0, errors: [] }
    
    // Import fetch for Node.js
    const { default: fetch } = await import('node-fetch')
    
    // Sync categories
    console.log('📂 Syncing categories...')
    try {
      const categoriesResponse = await fetch(`${POSTER_API_BASE}/menu.getCategories?token=${POSTER_TOKEN}`, {
        timeout: 30000
      })
      const categoriesData = await categoriesResponse.json()
      
      if (categoriesData.response) {
        for (const cat of categoriesData.response) {
          try {
            const [existing] = await connection.execute(
              'SELECT id FROM categories WHERE poster_id = ?',
              [cat.category_id]
            )
            
            if (existing.length > 0) {
              await connection.execute(`
                UPDATE categories SET name = ?, description = ?, sort_order = ?, updated_at = NOW()
                WHERE poster_id = ?
              `, [
                cat.category_name,
                `Category: ${cat.category_name}`,
                parseInt(cat.sort_order) || 0,
                cat.category_id
              ])
            } else {
              await connection.execute(`
                INSERT INTO categories (id, poster_id, name, description, sort_order, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
              `, [
                generateId(),
                cat.category_id,
                cat.category_name,
                `Category: ${cat.category_name}`,
                parseInt(cat.sort_order) || 0,
                1
              ])
            }
            results.categories++
          } catch (err) {
            console.error('Category sync error:', err.message)
            results.errors.push(`Category ${cat.category_name}: ${err.message}`)
          }
        }
      }
    } catch (error) {
      console.error('Categories sync failed:', error.message)
      results.errors.push(`Categories sync: ${error.message}`)
    }
    
    // Sync products (continue in next part due to length limit)
    console.log('🛍️  Syncing products...')
    try {
      const productsResponse = await fetch(`${POSTER_API_BASE}/menu.getProducts?token=${POSTER_TOKEN}`, {
        timeout: 30000
      })
      const productsData = await productsResponse.json()
      
      if (productsData.response) {
        for (const product of productsData.response) {
          try {
            // Smart categorization based on product name
            let categoryId = null
            const productName = (product.product_name || '').toLowerCase()

            // First try to find by poster_id if available
            if (product.category_id) {
              const [categoryResult] = await connection.execute(
                'SELECT id FROM categories WHERE poster_id = ? LIMIT 1',
                [product.category_id]
              )

              if (categoryResult.length > 0) {
                categoryId = categoryResult[0].id
              }
            }

            // Smart categorization based on product name patterns
            if (!categoryId) {
              // Get all categories for pattern matching
              const [allCategories] = await connection.execute('SELECT id, name, poster_id FROM categories')

              // Define categorization patterns
              const patterns = {
                'Пиво Скло Опілля': ['опілля', 'скло', 'пляшка'],
                'Пиво розлив': ['розлив', 'draft', 'кег'],
                'Пиво металева банка Опілля': ['банка', 'металева', 'опілля'],
                'Пиво ПЕТ Опілля': ['пет', 'пластик', 'опілля'],
                'пиво ж/б імпорт': ['імпорт', 'import', 'жб'],
                'Вино': ['вино', 'wine', 'червоне', 'біле'],
                'Сидр': ['сидр', 'cider'],
                'Закуски Вагові': ['ваг', 'вагов', 'кг', 'грам'],
                'Закуски штучні': ['штучн', 'шт', 'piece'],
                'Напої б/а': ['кола', 'пепсі', 'фанта', 'спрайт', 'сік', 'вода'],
                'Тара': ['тара', 'пляшка порожня', 'кег порожній'],
                'Інші': ['інш', 'other', 'різне']
              }

              // Try to match product name to category patterns
              for (const [categoryName, keywords] of Object.entries(patterns)) {
                if (keywords.some(keyword => productName.includes(keyword))) {
                  const category = allCategories.find(cat => cat.name === categoryName)
                  if (category) {
                    categoryId = category.id
                    break
                  }
                }
              }

              // If still no category, try beer-related patterns
              if (!categoryId && (productName.includes('пиво') || productName.includes('beer'))) {
                const beerCategory = allCategories.find(cat => cat.name === 'Пиво Скло Опілля')
                if (beerCategory) categoryId = beerCategory.id
              }

              // Default to snacks for food items
              if (!categoryId && (productName.includes('сир') || productName.includes('м\'ясо') ||
                                 productName.includes('риба') || productName.includes('ікра') ||
                                 productName.includes('арахіс') || productName.includes('горіх'))) {
                const snacksCategory = allCategories.find(cat => cat.name === 'Закуски штучні')
                if (snacksCategory) categoryId = snacksCategory.id
              }

              // Final fallback to general beverages
              if (!categoryId) {
                const beverageCategory = allCategories.find(cat => cat.name === 'Напої')
                if (beverageCategory) categoryId = beverageCategory.id
              }
            }
            
            if (!categoryId) continue
            
            // Calculate price
            let price = 0
            if (product.price) {
              if (typeof product.price === 'object') {
                price = parseFloat(product.price['1'] || product.price['0'] || 0) / 100
              } else {
                price = parseFloat(product.price) / 100
              }
            }
            
            const [existing] = await connection.execute(
              'SELECT id FROM products WHERE poster_id = ?',
              [product.product_id]
            )
            
            if (existing.length > 0) {
              await connection.execute(`
                UPDATE products SET name = ?, description = ?, price = ?, category_id = ?, updated_at = NOW()
                WHERE poster_id = ?
              `, [
                product.product_name,
                product.product_name,
                price,
                categoryId,
                product.product_id
              ])
            } else {
              await connection.execute(`
                INSERT INTO products (id, poster_id, category_id, name, description, price, is_active, stock_quantity, unit, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
              `, [
                generateId(),
                product.product_id,
                categoryId,
                product.product_name,
                product.product_name,
                price,
                1,
                0,
                'шт'
              ])
            }
            results.products++
          } catch (err) {
            console.error('Product sync error:', err.message)
            results.errors.push(`Product ${product.product_name}: ${err.message}`)
          }
        }
      }
    } catch (error) {
      console.error('Products sync failed:', error.message)
      results.errors.push(`Products sync: ${error.message}`)
    }
    
    console.log(`✅ Netlify sync completed: ${results.categories} categories, ${results.products} products`)
    
    res.json({
      success: true,
      message: 'Netlify sync completed successfully',
      results: results
    })
    
  } catch (error) {
    console.error('Netlify sync error:', error)
    res.status(500).json({
      success: false,
      error: error.message
    })
  } finally {
    if (connection) {
      await connection.end()
    }
  }
})

// Analytics endpoints (placeholder)
app.post('/api/analytics/event', (req, res) => {
  console.log('📊 Analytics event:', req.body.event || 'unknown')
  res.json({ success: true, message: 'Event logged' })
})

app.get('/api/analytics/dashboard', (req, res) => {
  res.json({
    success: true,
    data: {
      overview: { total_events: 0, total_sessions: 0, unique_users: 0, conversion_rate: 0 },
      top_events: [],
      top_pages: [],
      device_breakdown: {},
      timeframe: req.query.timeframe || '7d'
    }
  })
})

// Error handling
app.use((err, req, res, next) => {
  console.error('API Error:', err)
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: err.message
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.originalUrl
  })
})

// Export handler for Netlify Functions
export const handler = serverless(app)
