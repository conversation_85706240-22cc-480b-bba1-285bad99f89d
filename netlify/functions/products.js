// Products endpoint
import mysql from 'mysql2/promise'

const dbConfig = {
  host: 'avalon.cityhost.com.ua',
  port: 3306,
  user: 'ch6edd8920_pwapos',
  password: 'mA1ZDUY7fA',
  database: 'ch6edd8920_pwapos',
  charset: 'utf8mb4'
}

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
  'Content-Type': 'application/json'
}

export const handler = async (event, context) => {
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' }
  }

  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ success: false, error: 'Method not allowed' })
    }
  }

  try {
    const connection = await mysql.createConnection(dbConfig)
    
    let query = `
      SELECT p.*, c.name as category_name 
      FROM products p 
      LEFT JOIN categories c ON p.category_id = c.id 
      WHERE p.is_active = 1
    `
    
    const params = []
    
    // Parse query parameters
    const queryParams = event.queryStringParameters || {}
    
    // Filter by category if provided
    if (queryParams.category_id) {
      query += ' AND p.category_id = ?'
      params.push(queryParams.category_id)
    }
    
    // Search by name if provided
    if (queryParams.search) {
      query += ' AND p.name LIKE ?'
      params.push(`%${queryParams.search}%`)
    }
    
    query += ' ORDER BY p.name'
    
    const [products] = await connection.execute(query, params)
    await connection.end()
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        products: products
      })
    }
  } catch (error) {
    console.error('Products error:', error)
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        success: false, 
        error: 'Failed to fetch products',
        message: error.message 
      })
    }
  }
}
