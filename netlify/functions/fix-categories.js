const mysql = require('mysql2/promise')

exports.handler = async (event, context) => {
  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: 'Method not allowed' })
    }
  }

  try {
    // Database connection
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      ssl: {
        rejectUnauthorized: false
      }
    })

    console.log('🔄 Starting category fix...')

    // Get all categories
    const [categories] = await connection.execute('SELECT id, name FROM categories')
    console.log(`📊 Found ${categories.length} categories`)

    // Get all products
    const [products] = await connection.execute('SELECT id, name, category_id FROM products')
    console.log(`📊 Found ${products.length} products`)

    // Create category lookup
    const categoryLookup = {}
    categories.forEach(cat => {
      categoryLookup[cat.name] = cat.id
    })

    // Smart categorization patterns
    const patterns = {
      'Пиво Скло Опілля': ['опілля', 'скло', 'пляшка'],
      'Пиво розлив': ['розлив', 'draft', 'кег'],
      'Пиво металева банка Опілля': ['банка', 'металева', 'опілля'],
      'Пиво ПЕТ Опілля': ['пет', 'пластик', 'опілля'],
      'пиво ж/б імпорт': ['імпорт', 'import', 'жб'],
      'Вино': ['вино', 'wine', 'червоне', 'біле'],
      'Сидр': ['сидр', 'cider'],
      'Закуски Вагові': ['ваг', 'вагов', 'кг', 'грам'],
      'Закуски штучні': ['штучн', 'шт', 'piece', 'арахіс', 'сир', 'ікра', 'горіх'],
      'Напої б/а': ['кола', 'пепсі', 'фанта', 'спрайт', 'сік', 'вода'],
      'Тара': ['тара', 'пляшка порожня', 'кег порожній', 'кришка', 'стаканчик'],
      'Інші': ['інш', 'other', 'різне']
    }

    let updates = 0
    const updatePromises = []

    for (const product of products) {
      const productName = (product.name || '').toLowerCase()
      let newCategoryId = null

      // Try to match product name to category patterns
      for (const [categoryName, keywords] of Object.entries(patterns)) {
        if (keywords.some(keyword => productName.includes(keyword))) {
          if (categoryLookup[categoryName]) {
            newCategoryId = categoryLookup[categoryName]
            break
          }
        }
      }

      // If still no category, try beer-related patterns
      if (!newCategoryId && (productName.includes('пиво') || productName.includes('beer'))) {
        if (categoryLookup['Пиво Скло Опілля']) {
          newCategoryId = categoryLookup['Пиво Скло Опілля']
        }
      }

      // Default to snacks for food items
      if (!newCategoryId && (productName.includes('сир') || productName.includes("м'ясо") || 
                             productName.includes('риба') || productName.includes('ікра') ||
                             productName.includes('арахіс') || productName.includes('горіх'))) {
        if (categoryLookup['Закуски штучні']) {
          newCategoryId = categoryLookup['Закуски штучні']
        }
      }

      // Final fallback to general beverages
      if (!newCategoryId) {
        if (categoryLookup['Напої']) {
          newCategoryId = categoryLookup['Напої']
        }
      }

      // Update product if category changed
      if (newCategoryId && newCategoryId !== product.category_id) {
        console.log(`📝 Updating "${product.name}" to category ${newCategoryId}`)
        
        const updatePromise = connection.execute(
          'UPDATE products SET category_id = ? WHERE id = ?',
          [newCategoryId, product.id]
        ).then(() => {
          updates++
        }).catch(error => {
          console.error(`❌ Failed to update ${product.name}:`, error.message)
        })
        
        updatePromises.push(updatePromise)
      }
    }

    // Wait for all updates to complete
    await Promise.all(updatePromises)

    // Get final category counts
    const [finalCounts] = await connection.execute(`
      SELECT 
        c.name as category_name,
        COUNT(p.id) as product_count
      FROM categories c
      LEFT JOIN products p ON c.id = p.category_id
      GROUP BY c.id, c.name
      ORDER BY product_count DESC
    `)

    await connection.end()

    console.log(`✅ Updated ${updates} products successfully!`)

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: true,
        message: `Successfully updated ${updates} products`,
        categoryCounts: finalCounts
      })
    }

  } catch (error) {
    console.error('❌ Error:', error)
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    }
  }
}
