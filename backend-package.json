{"name": "backend-posterpos-shop", "version": "1.0.0", "description": "PWA POS Shop Backend API with Banner Management and Admin Authentication", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "npx prisma generate && npx prisma migrate deploy", "db:migrate": "npx prisma migrate deploy", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:seed": "node scripts/add-banners-table.js", "db:analytics": "node scripts/add-analytics-tables.js", "db:test": "node test-database.js", "db:fix": "node fix-database-sync.js", "db:update-prisma": "node update-prisma-schema.js", "db:manual-sync": "node manual-sync.js", "start:simple": "node start-server-simple.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "@prisma/client": "^5.6.0", "prisma": "^5.6.0", "nodemailer": "^6.9.7", "mysql2": "^3.6.5", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": "18.x"}, "keywords": ["express", "api", "pos", "ecommerce", "banner-management", "prisma", "postgresql"], "author": "PWA POS Shop", "license": "MIT"}