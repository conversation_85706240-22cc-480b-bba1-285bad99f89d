# Database Configuration (MySQL for shared hosting)
DATABASE_URL=mysql://ch6edd8920_pwapos:<EMAIL>:3306/ch6edd8920_pwapos

# Alternative MySQL configuration (if DATABASE_URL doesn't work)
DB_HOST=avalon.cityhost.com.ua
DB_PORT=3306
DB_USER=ch6edd8920_pwapos
DB_PASSWORD=mA1ZDUY7fA
DB_NAME=ch6edd8920_pwapos

# Server Configuration
PORT=3001
NODE_ENV=production

# CORS Configuration
CORS_ORIGIN=https://your-frontend.netlify.app

# Poster POS API Integration
POSTER_API_TOKEN=218047:05891220e474bad7f26b6eaa0be3f344

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=public/images

# Email Service (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Viber Integration (Optional)
VIBER_BOT_TOKEN=your-viber-bot-token

# Security
JWT_SECRET=your-jwt-secret-key
