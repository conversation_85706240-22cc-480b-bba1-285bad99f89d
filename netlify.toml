[build]
  publish = "dist"
  command = "npm run build"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

[functions]
  node_bundler = "esbuild"

# Redirect API calls to individual Netlify Functions
[[redirects]]
  from = "/api/health"
  to = "/.netlify/functions/health"
  status = 200

[[redirects]]
  from = "/api/products"
  to = "/.netlify/functions/products"
  status = 200

[[redirects]]
  from = "/api/categories"
  to = "/.netlify/functions/categories"
  status = 200

[[redirects]]
  from = "/api/branches"
  to = "/.netlify/functions/branches"
  status = 200

[[redirects]]
  from = "/api/sync/full"
  to = "/.netlify/functions/sync-full"
  status = 200

[[redirects]]
  from = "/api/banners"
  to = "/.netlify/functions/banners"
  status = 200

[[redirects]]
  from = "/api/orders"
  to = "/.netlify/functions/orders"
  status = 200

[[redirects]]
  from = "/api/site-config"
  to = "/.netlify/functions/site-config"
  status = 200

[[redirects]]
  from = "/api/debug"
  to = "/.netlify/functions/debug"
  status = 200

[[redirects]]
  from = "/api/fix-categories"
  to = "/.netlify/functions/fix-categories"
  status = 200

[[redirects]]
  from = "/api/categorize-products"
  to = "/.netlify/functions/categorize-products"
  status = 200

[[redirects]]
  from = "/api/test-db"
  to = "/.netlify/functions/test-db"
  status = 200

[[redirects]]
  from = "/api/update-stock"
  to = "/.netlify/functions/update-stock"
  status = 200

# Redirect admin routes to index.html for SPA
[[redirects]]
  from = "/admin/*"
  to = "/index.html"
  status = 200

# Redirect shop routes to index.html for SPA
[[redirects]]
  from = "/shop/*"
  to = "/index.html"
  status = 200

# Catch-all redirect for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# CORS headers for API
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Origin, X-Requested-With, Content-Type, Accept, Authorization"

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(self)"

# PWA manifest headers
[[headers]]
  for = "/manifest.json"
  [headers.values]
    Content-Type = "application/manifest+json"
    Cache-Control = "public, max-age=0, must-revalidate"

# Service worker headers
[[headers]]
  for = "/sw.js"
  [headers.values]
    Content-Type = "application/javascript"
    Cache-Control = "public, max-age=0, must-revalidate"

# Static assets caching
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# JavaScript and CSS caching
[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Image caching
[[headers]]
  for = "/*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.webp"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Font caching
[[headers]]
  for = "/*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# API proxy for development (optional)
# [[redirects]]
#   from = "/api/*"
#   to = "https://your-backend-api.com/api/:splat"
#   status = 200
#   force = true

# Environment-specific redirects
[context.production]
  command = "npm run build"

[context.deploy-preview]
  command = "npm run build"

[context.branch-deploy]
  command = "npm run build"
