# 🚀 CityHost.com.ua Deployment Guide

Complete deployment guide for your PWA POS Shop on CityHost.com.ua with your specific MySQL credentials.

## 📋 Your Database Configuration

- **Host**: avalon.cityhost.com.ua
- **Database**: ch6edd8920_pwapos
- **Username**: ch6edd8920_pwapos
- **Password**: mA1ZDUY7fA
- **Port**: 3306

## 🔧 Step 1: Database Setup

### 1.1 Import Database Schema

1. **Login to your CityHost.com.ua cPanel**
2. **Go to phpMyAdmin**
3. **Select database**: `ch6edd8920_pwapos`
4. **Click "Import" tab**
5. **Upload file**: `pwa_pos_shop_database.sql`
6. **Click "Go" to import**

### 1.2 Verify Database Import

After import, you should see these tables:
- ✅ `categories` (4 sample categories)
- ✅ `products` (empty, will sync from Poster POS)
- ✅ `branches` (6 Opillia locations in Kyiv)
- ✅ `banners` (1 welcome banner)
- ✅ `orders` (empty)
- ✅ `customers` (empty)
- ✅ `analytics_events` (empty)
- ✅ `analytics_sessions` (empty)
- ✅ `site_config` (basic settings)

## 🌐 Step 2: Backend Deployment

### 2.1 Upload Backend Files

Upload your `/server` folder contents to your hosting:

**Required files:**
```
server/
├── index.js
├── package.json
├── .env (already configured)
├── routes/
├── prisma/
├── public/
└── scripts/
```

### 2.2 Install Dependencies

SSH into your hosting or use terminal:
```bash
cd /path/to/your/server
npm install
```

### 2.3 Test Database Connection

```bash
node test-database.js
```

Expected output:
```
✅ Database connection successful!
✅ Found existing tables:
   - categories
   - products
   - branches
   - banners
   - orders
   - customers
   - analytics_events
   - analytics_sessions
   - site_config
✅ Categories table: 4 records
✅ Banners table: 1 records
✅ Branches table: 6 records
```

### 2.4 Generate Prisma Client

```bash
npx prisma generate
```

### 2.5 Start Server

```bash
npm start
```

Your backend will be available at: `https://your-domain.com`

## 📱 Step 3: Frontend Configuration

### 3.1 Update Frontend Environment

In your frontend `.env`:
```env
VITE_API_BASE_URL=https://your-backend-domain.com
VITE_POSTER_API_TOKEN=218047:05891220e474bad7f26b6eaa0be3f344
VITE_ADMIN_PASSWORD=admin123
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

### 3.2 Update CORS in Backend

Update your backend `.env`:
```env
CORS_ORIGIN=https://your-frontend.netlify.app
```

## 🧪 Step 4: Testing

### 4.1 Test API Endpoints

```bash
# Health check
curl https://your-domain.com/health

# Get banners
curl https://your-domain.com/api/banners

# Get branches
curl https://your-domain.com/api/branches

# Get categories
curl https://your-domain.com/api/categories
```

### 4.2 Test Admin Panel

1. Go to: `https://your-frontend.netlify.app/admin`
2. Enter password: `admin123`
3. Check "Banners" tab
4. Check "Analytics" tab

### 4.3 Test Banner Management

1. In admin panel → Banners
2. Try creating a new banner
3. Upload an image
4. Check if it appears on homepage

## 🔧 Step 5: Poster POS Integration

### 5.1 Sync Products

Your system will automatically sync with Poster POS API:
- **Token**: 218047:05891220e474bad7f26b6eaa0be3f344
- **Endpoints**: Products, categories, inventory
- **Sync frequency**: Every 30 minutes

### 5.2 Test Product Sync

```bash
# Manual sync (if needed)
curl -X POST https://your-domain.com/api/sync/products
```

## 📊 Step 6: Analytics Setup

### 6.1 Google Analytics (Optional)

1. Create GA4 property
2. Get Measurement ID
3. Add to frontend `.env`:
   ```env
   VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX
   ```

### 6.2 Custom Analytics

Your system includes built-in analytics:
- ✅ Page views tracking
- ✅ E-commerce events
- ✅ User sessions
- ✅ Admin actions
- ✅ Error tracking

View analytics at: `/admin` → "Analytics" tab

## 🔐 Step 7: Security Configuration

### 7.1 Change Default Passwords

Update in backend `.env`:
```env
ADMIN_PASSWORD=your_secure_password
JWT_SECRET=your_secure_jwt_secret
```

Update in frontend `.env`:
```env
VITE_ADMIN_PASSWORD=your_secure_password
```

### 7.2 SSL Certificate

Ensure your hosting has SSL enabled:
- ✅ HTTPS for backend
- ✅ HTTPS for frontend
- ✅ Secure cookie settings

## 📋 Step 8: Final Checklist

### Backend Checklist:
- [ ] Database imported successfully
- [ ] Environment variables configured
- [ ] Dependencies installed
- [ ] Prisma client generated
- [ ] Server starts without errors
- [ ] API endpoints respond correctly
- [ ] File upload directories created
- [ ] CORS configured for frontend

### Frontend Checklist:
- [ ] Environment variables set
- [ ] Backend API URL configured
- [ ] Admin panel accessible
- [ ] Banner slider works
- [ ] Product catalog loads
- [ ] Shopping cart functions
- [ ] Analytics tracking active

### Integration Checklist:
- [ ] Poster POS API connection works
- [ ] Product sync functioning
- [ ] Order creation works
- [ ] Email notifications sent
- [ ] Analytics data collected

## 🚨 Troubleshooting

### Database Connection Issues:
```bash
# Test connection
node test-database.js

# Check MySQL logs
tail -f /var/log/mysql/error.log
```

### API Not Working:
```bash
# Check server logs
npm start

# Test specific endpoint
curl -v https://your-domain.com/api/banners
```

### CORS Errors:
1. Check `CORS_ORIGIN` in backend `.env`
2. Ensure frontend URL matches exactly
3. Restart backend server

## 📞 Support

### CityHost.com.ua Support:
- **Website**: cityhost.com.ua
- **Support**: Check your hosting panel

### Database Issues:
1. Check phpMyAdmin access
2. Verify table structure
3. Check user permissions

## 🎉 Success!

Your PWA POS Shop is now live with:
- ✅ **Backend**: Running on CityHost.com.ua
- ✅ **Database**: MySQL with all tables
- ✅ **Frontend**: Deployed to Netlify
- ✅ **Analytics**: Tracking user behavior
- ✅ **Admin Panel**: Banner management
- ✅ **Poster POS**: API integration

**URLs:**
- **Frontend**: https://your-site.netlify.app
- **Backend**: https://your-domain.com
- **Admin**: https://your-site.netlify.app/admin

Your PWA POS Shop is ready for business! 🚀🎉
