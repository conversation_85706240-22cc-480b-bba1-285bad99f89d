# 🗄️ Database Import Guide - CityHost.com.ua

Fixed SQL files for your MySQL hosting with proper compatibility.

## 📁 SQL Files Available

### **Option 1: Modern MySQL (Recommended)**
**File**: `pwa_pos_shop_database_compatible.sql`
- ✅ **For**: MySQL 5.7+ with JSON support
- ✅ **Features**: Full functionality with JSON fields
- ✅ **Best for**: Modern shared hosting

### **Option 2: Legacy MySQL (Maximum Compatibility)**
**File**: `pwa_pos_shop_database_simple.sql`
- ✅ **For**: MySQL 5.6+ (all shared hosting)
- ✅ **Features**: All functionality, TEXT instead of JSON
- ✅ **Best for**: Older hosting or if Option 1 fails

## 🚀 Import Instructions

### **Step 1: Login to cPanel**
1. Go to your CityHost.com.ua control panel
2. Login with your hosting credentials

### **Step 2: Access phpMyAdmin**
1. Find "Databases" section in cPanel
2. Click "phpMyAdmin"
3. Select database: `ch6edd8920_pwapos`

### **Step 3: Import Database**

#### **Try Option 1 First (Modern):**
1. Click "Import" tab in phpMyAdmin
2. Click "Choose File"
3. Select: **`pwa_pos_shop_database_compatible.sql`**
4. Click "Go"

#### **If Option 1 Fails, Use Option 2:**
1. Clear any partial import (drop tables if needed)
2. Click "Import" tab
3. Select: **`pwa_pos_shop_database_simple.sql`**
4. Click "Go"

### **Step 4: Verify Import**

After successful import, you should see:

**Tables Created (11 total):**
- ✅ `categories` (4 records)
- ✅ `products` (empty - will sync from Poster POS)
- ✅ `branches` (6 Opillia locations)
- ✅ `banners` (1 welcome banner)
- ✅ `orders` (empty)
- ✅ `order_items` (empty)
- ✅ `customers` (empty)
- ✅ `analytics_events` (empty)
- ✅ `analytics_sessions` (empty)
- ✅ `product_inventory` (empty)
- ✅ `site_config` (9 configuration settings)

## 🔧 Test Database Connection

After import, test your connection:

```bash
cd server
node test-database.js
```

**Expected Output:**
```
✅ Database connection successful!
✅ Found existing tables:
   - categories
   - products
   - branches
   - banners
   - orders
   - order_items
   - customers
   - analytics_events
   - analytics_sessions
   - product_inventory
   - site_config
✅ Categories table: 4 records
✅ Banners table: 1 records
✅ Branches table: 6 records
```

## 📊 What's Included

### **Sample Categories:**
- Напої (Beverages)
- Закуски (Snacks)
- Основні страви (Main dishes)
- Десерти (Desserts)

### **Opillia Branches (6 locations):**
- Центр (Center) - вул. Костянтина Данькевича, 10
- Поділ (Podil) - вул. Сагайдачного, 25
- Печерськ (Pechersk) - вул. Бессарабська, 5
- Оболонь (Obolon) - просп. Оболонський, 15
- Троєщина (Troieshchyna) - вул. Лаврухіна, 8
- Дарниця (Darnytsia) - вул. Харківське шосе, 201

### **Site Configuration:**
- Delivery fees: 99 UAH base + 30 UAH/km
- Minimum order: 300 UAH
- Contact: +38 (097) 324 46 68
- Email: <EMAIL>
- Hours: 10:00-22:00 daily

### **Welcome Banner:**
- Title: "Ласкаво просимо до Опілля!"
- Subtitle: "Найкращі напої та закуски з доставкою по Києву"

## 🚨 Troubleshooting

### **Import Fails with Syntax Error:**
- Use `pwa_pos_shop_database_simple.sql` instead
- This version is compatible with older MySQL

### **Character Encoding Issues:**
1. In phpMyAdmin, go to "Operations" tab
2. Set "Collation" to `utf8_unicode_ci`
3. Re-import the SQL file

### **Permission Denied:**
- Contact CityHost.com.ua support
- Verify database user has full privileges

### **Tables Not Created:**
1. Check for error messages in phpMyAdmin
2. Try importing one table at a time
3. Use the simple version SQL file

## ✅ Success Checklist

After successful import:
- [ ] 11 tables created
- [ ] 4 categories inserted
- [ ] 6 branches inserted
- [ ] 1 banner inserted
- [ ] 9 site config entries
- [ ] Database connection test passes
- [ ] Backend server starts successfully

## 🔄 Next Steps

1. **Import Database** ✅
2. **Test Connection**: `node test-database.js`
3. **Start Backend**: `npm start`
4. **Deploy Frontend**: Update API URL
5. **Test System**: Check admin panel

## 📞 Support

### **If Import Fails:**
1. Try the simple version SQL file
2. Check MySQL version in cPanel
3. Contact CityHost.com.ua support

### **Database Issues:**
- **Host**: avalon.cityhost.com.ua
- **Database**: ch6edd8920_pwapos
- **User**: ch6edd8920_pwapos
- **Password**: mA1ZDUY7fA

Your database is ready for the PWA POS Shop! 🎉
