# 🚀 PWA POS Backend API

Backend API for PWA POS Shop with comprehensive banner management, admin authentication, and e-commerce functionality.

## ✨ Features

- 🎨 **Banner Management** - CRUD operations with image upload and reordering
- 🔐 **Admin Authentication** - Secure admin panel access
- 🛒 **Product Catalog** - Integration with Poster POS API
- 📦 **Order Processing** - Complete order management system
- 🏪 **Branch Management** - Multi-location support
- 📊 **Inventory Tracking** - Real-time stock levels
- 📧 **Email Services** - Order confirmations and notifications
- 🔄 **Data Sync** - Poster POS integration with automatic sync
- 📤 **File Upload** - Image handling with multer

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Setup
```bash
cp .env.example .env
# Edit .env with your configuration
```

### 3. Database Setup
```bash
# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Seed initial data (optional)
npm run db:seed
```

### 4. Start Development Server
```bash
npm run dev
```

### 5. Start Production Server
```bash
npm start
```

## 📡 API Endpoints

### 🎨 Banner Management
- `GET /api/banners` - Get active banners for frontend
- `GET /api/banners/admin` - Get all banners for admin panel
- `POST /api/banners` - Create new banner (with image upload)
- `PUT /api/banners/:id` - Update existing banner
- `DELETE /api/banners/:id` - Delete banner
- `PUT /api/banners/reorder` - Reorder banners (drag & drop)

### 🛍️ Products
- `GET /api/products` - Get products with inventory
- `GET /api/products/:id` - Get product by ID
- `PUT /api/products/:id` - Update product
- `POST /api/products/bulk-edit` - Bulk edit products

### 📂 Categories
- `GET /api/categories` - Get all categories

### 🏪 Branches
- `GET /api/branches` - Get all branches
- `PUT /api/branches/:id` - Update branch information

### 📦 Orders
- `POST /api/orders` - Create new order
- `GET /api/orders` - Get orders (admin)
- `GET /api/orders/:id` - Get order by ID
- `PUT /api/orders/:id` - Update order status

### 🔄 Data Sync
- `POST /api/sync/full` - Full sync with Poster POS
- `POST /api/sync/inventory` - Sync inventory only
- `POST /api/sync/images` - Download product images

### 📤 File Upload
- `POST /api/upload` - Upload files (images for banners)

### ⚙️ Site Configuration
- `GET /api/site-config` - Get site configuration
- `PUT /api/site-config` - Update site configuration

## 🌐 Deployment

### 🚂 Railway (Recommended)

1. **Create Account**: [railway.app](https://railway.app)
2. **New Project**: Deploy from GitHub
3. **Add Database**: PostgreSQL service
4. **Environment Variables**: Set required variables
5. **Deploy**: Automatic deployment

### 🎨 Render Alternative

1. **Create Account**: [render.com](https://render.com)
2. **New Web Service**: Connect GitHub
3. **Add Database**: PostgreSQL
4. **Environment Variables**: Configure
5. **Deploy**: Automatic builds

## 🔐 Environment Variables

### Required
```env
DATABASE_URL=postgresql://user:pass@host:port/db
PORT=3001
NODE_ENV=production
CORS_ORIGIN=https://your-frontend.netlify.app
```

### Optional
```env
# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=public/images

# Poster POS API
POSTER_API_TOKEN=218047:05891220e474bad7f26b6eaa0be3f344
```

## 🗄️ Database Schema

### Core Tables
- **banners** - Homepage/shop banners with image management
- **products** - Product catalog synced with Poster POS
- **categories** - Product categories
- **branches** - Store locations
- **orders** - Customer orders with status tracking
- **customers** - Customer data
- **product_inventory** - Real-time stock levels
- **site_config** - Site configuration settings

### Banner Schema
```sql
CREATE TABLE banners (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  subtitle TEXT,
  image_url TEXT,
  link_url TEXT,
  link_text TEXT,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 📁 Project Structure

```
backend-posterpos-shop/
├── routes/              # API route handlers
│   ├── banners.js      # Banner CRUD operations
│   ├── products.js     # Product management
│   ├── orders.js       # Order processing
│   ├── upload.js       # File upload handling
│   ├── branches.js     # Branch management
│   ├── categories.js   # Category operations
│   ├── sync.js         # Poster POS sync
│   └── siteConfig.js   # Site configuration
├── services/           # Business logic
│   ├── database.js     # Database utilities
│   ├── emailService.js # Email handling
│   ├── imageService.js # Image processing
│   └── viberService.js # Viber notifications
├── prisma/             # Database schema
│   ├── schema.prisma   # Prisma schema
│   └── migrations/     # Database migrations
├── public/             # Static files
│   └── images/         # Uploaded images
│       ├── banners/    # Banner images
│       └── products/   # Product images
├── scripts/            # Utility scripts
│   ├── add-banners-table.js
│   └── database-setup.js
└── index.js           # Main server file
```

## 🧪 Testing

### Health Check
```bash
curl https://your-backend.railway.app/health
```

### API Testing
```bash
# Get banners
curl https://your-backend.railway.app/api/banners

# Get products
curl https://your-backend.railway.app/api/products

# Test CORS
curl -H "Origin: https://your-frontend.netlify.app" \
     https://your-backend.railway.app/api/banners
```

## 🔗 Frontend Integration

This backend is designed to work with:
- **Frontend Repository**: https://github.com/waverhan/posterpos-shop.git
- **Frontend URL**: https://your-frontend.netlify.app

### Frontend Environment Variables
```env
VITE_API_BASE_URL=https://your-backend.railway.app
VITE_POSTER_API_TOKEN=218047:05891220e474bad7f26b6eaa0be3f344
VITE_ADMIN_PASSWORD=your_secure_admin_password
```

## 🎯 Production Checklist

- [ ] Environment variables configured
- [ ] Database connected and migrated
- [ ] CORS origins set correctly
- [ ] File upload directories created
- [ ] Health check endpoint responding
- [ ] SSL certificate configured
- [ ] Error logging enabled
- [ ] Backup strategy implemented
- [ ] Admin authentication working

## 📞 Support

- **Backend Repository**: https://github.com/waverhan/backend-posterpos-shop.git
- **Frontend Repository**: https://github.com/waverhan/posterpos-shop.git
- **Issues**: GitHub Issues for bug reports
- **Documentation**: See deployment guides

Ready to power your PWA POS Shop! 🚀
