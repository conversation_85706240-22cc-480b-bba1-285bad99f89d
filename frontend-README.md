# 🛒 PWA POS Shop - Frontend

Modern Progressive Web App for e-commerce with dynamic banner slider, admin protection, and comprehensive POS functionality.

## ✨ Features

### 🎨 **Dynamic Content Management**
- **Banner Slider** - Homepage and shop page banners with auto-sliding
- **Admin Panel** - Password-protected admin access at `/admin`
- **Image Management** - Upload and manage banner images
- **Content Control** - Real-time content updates

### 🛒 **E-commerce Functionality**
- **Product Catalog** - Integration with Poster POS API
- **Shopping Cart** - Smart cart with recommendations
- **Checkout System** - Complete order processing
- **Inventory Validation** - Real-time stock checking

### 📱 **PWA Capabilities**
- **Installable** - Add to home screen on mobile/desktop
- **Offline Support** - Works without internet connection
- **Push Notifications** - Order updates and promotions
- **Background Sync** - Sync data when connection restored

### 🎯 **Location-Based Services**
- **Delivery Options** - Location-based delivery pricing
- **Branch Selection** - Choose pickup locations
- **Geolocation** - Automatic location detection
- **Address Autocomplete** - Ukrainian address suggestions

### 🤖 **AI-Powered Features**
- **Product Recommendations** - Smart product suggestions
- **Chat Assistant** - AI-powered customer support
- **Analytics** - Customer behavior insights

### 🌍 **Multilingual Support**
- **Ukrainian** - Primary language with full localization
- **English** - Secondary language support
- **Dynamic Switching** - Real-time language changes

### 🎨 **Modern UI/UX**
- **Responsive Design** - Works on all devices
- **Dark/Light Mode** - Theme switching
- **Smooth Animations** - Professional transitions
- **Touch-Friendly** - Optimized for mobile interaction

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Setup
Create `.env` file:
```env
# Backend API
VITE_API_BASE_URL=https://your-backend.railway.app

# Poster POS Integration
VITE_POSTER_API_TOKEN=218047:05891220e474bad7f26b6eaa0be3f344

# Admin Panel Security
VITE_ADMIN_PASSWORD=your_secure_admin_password

# Optional: AI Features
VITE_OPENAI_API_KEY=your_openai_key
```

### 3. Start Development
```bash
npm run dev
```

### 4. Build for Production
```bash
npm run build
```

### 5. Preview Production Build
```bash
npm run preview
```

## 🔐 Admin Panel Access

### Accessing Admin Panel
1. **Navigate to**: `/admin`
2. **Enter Password**: Use password from `VITE_ADMIN_PASSWORD`
3. **Default Password**: `admin123` (change for production!)

### Admin Features
- 🎨 **Banner Management** - Create, edit, reorder banners
- 📦 **Order Management** - View and update orders
- 🛍️ **Product Management** - Edit products and sync with Poster POS
- 🏪 **Branch Management** - Manage store locations
- ⚙️ **Site Configuration** - Update site settings
- 📊 **Analytics** - View recommendation analytics

### Security Features
- ✅ **Password Protection** - Environment-based password
- ✅ **Session Persistence** - Stay logged in across visits
- ✅ **No Public Links** - Admin panel not discoverable by users
- ✅ **Mobile Responsive** - Full admin functionality on mobile

## 🌐 Deployment

### Netlify (Recommended)

1. **Connect Repository**
   - Go to [netlify.com](https://netlify.com)
   - "New site from Git" → GitHub
   - Select `waverhan/posterpos-shop`

2. **Build Settings**
   ```
   Build command: npm run build
   Publish directory: dist
   Node version: 18
   ```

3. **Environment Variables**
   ```env
   VITE_API_BASE_URL=https://your-backend.railway.app
   VITE_POSTER_API_TOKEN=218047:05891220e474bad7f26b6eaa0be3f344
   VITE_ADMIN_PASSWORD=your_secure_password
   ```

4. **Deploy**
   - Click "Deploy site"
   - Your site will be live at: `https://your-site-name.netlify.app`

### Alternative Deployment Options
- **Vercel** - Similar to Netlify with GitHub integration
- **Firebase Hosting** - Google's hosting platform
- **GitHub Pages** - Free hosting for static sites

## 📱 Mobile App (Capacitor)

### Build for Mobile
```bash
# Add platforms
npm run cap:add ios
npm run cap:add android

# Build and sync
npm run cap:build

# Open in IDE
npm run cap:open ios
npm run cap:open android
```

### Mobile Features
- **Native Performance** - Capacitor.js integration
- **Device APIs** - Camera, geolocation, notifications
- **App Store Ready** - Build for iOS and Android
- **Offline First** - Works without internet

## 🔗 Backend Integration

This frontend connects to a separate backend API:

### Backend Repository
- **URL**: https://github.com/waverhan/backend-posterpos-shop.git
- **Deployment**: Railway or Render
- **Features**: Banner management, order processing, file uploads

### API Integration
- **RESTful API** - Standard HTTP methods
- **Real-time Updates** - Live inventory and order status
- **File Upload** - Image handling for banners
- **Authentication** - Admin session management

## 🧪 Testing

### Development Testing
```bash
# Run development server
npm run dev

# Type checking
npm run type-check

# Linting
npm run lint

# Build test
npm run test:build
```

### Production Testing
```bash
# Build for production
npm run build

# Preview production build
npm run preview

# Test PWA features
# - Install prompt
# - Offline functionality
# - Push notifications
```

## 📊 Performance

### Optimization Features
- **Code Splitting** - Lazy loading of routes
- **Image Optimization** - Responsive images
- **Caching Strategy** - Service worker caching
- **Bundle Analysis** - Webpack bundle analyzer

### Performance Metrics
- **Lighthouse Score** - 90+ on all metrics
- **Core Web Vitals** - Optimized for Google rankings
- **Mobile Performance** - Fast loading on 3G networks

## 🔧 Configuration

### Environment Variables
```env
# Required
VITE_API_BASE_URL=https://your-backend.railway.app
VITE_POSTER_API_TOKEN=218047:05891220e474bad7f26b6eaa0be3f344
VITE_ADMIN_PASSWORD=your_secure_password

# Optional
VITE_OPENAI_API_KEY=your_openai_key
VITE_GOOGLE_MAPS_API_KEY=your_maps_key
VITE_ANALYTICS_ID=your_analytics_id
```

### Build Configuration
- **Vite** - Fast build tool with HMR
- **TypeScript** - Type safety and better DX
- **Tailwind CSS** - Utility-first CSS framework
- **PWA Plugin** - Service worker and manifest

## 📞 Support

### Documentation
- **Admin Access**: See `ADMIN_ACCESS.md`
- **Deployment**: See deployment guides
- **API Integration**: Check backend repository

### Troubleshooting
- **Build Issues**: Check Node.js version (18+)
- **Admin Access**: Verify `VITE_ADMIN_PASSWORD`
- **API Errors**: Check backend URL and CORS settings

### Links
- **Frontend Repository**: https://github.com/waverhan/posterpos-shop.git
- **Backend Repository**: https://github.com/waverhan/backend-posterpos-shop.git
- **Live Demo**: https://your-site-name.netlify.app

## 🎉 Success!

Your PWA POS Shop frontend is ready for deployment with:
- ✅ Dynamic banner slider
- ✅ Protected admin panel
- ✅ PWA capabilities
- ✅ Mobile-responsive design
- ✅ Production-ready configuration

Deploy to Netlify and start managing your e-commerce platform! 🚀
