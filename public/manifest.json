{"name": "PWA POS Shop", "short_name": "POS Shop", "description": "Progressive Web App for POS Shop with Poster integration", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "portrait-primary", "scope": "/", "lang": "uk", "categories": ["shopping", "food", "business"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "Shop", "short_name": "Shop", "description": "Browse products", "url": "/shop", "icons": [{"src": "/icons/icon-96x96.png", "sizes": "96x96"}]}, {"name": "<PERSON><PERSON>", "short_name": "<PERSON><PERSON>", "description": "View cart", "url": "/cart", "icons": [{"src": "/icons/icon-96x96.png", "sizes": "96x96"}]}, {"name": "Orders", "short_name": "Orders", "description": "View orders", "url": "/orders", "icons": [{"src": "/icons/icon-96x96.png", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}