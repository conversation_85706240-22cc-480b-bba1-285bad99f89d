{"name": "pwa-pos-system", "version": "1.0.0", "description": "Modern PWA POS System with Capacitor.js and Poster API integration", "scripts": {"dev": "vite", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "vite build", "build:backend": "echo 'Backend build complete - using Netlify Functions'", "build:prod": "node scripts/prepare-production.js && vite build", "prepare:prod": "node scripts/prepare-production.js", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "setup": "./setup.sh", "setup:win": "node setup.js", "deploy:netlify": "npm run build:prod && netlify deploy --prod", "deploy:preview": "npm run build && netlify deploy", "analyze": "npm run build -- --analyze", "test:build": "npm run build && npm run preview", "cap:add": "npx cap add", "cap:copy": "npx cap copy", "cap:sync": "npx cap sync", "cap:open": "npx cap open", "cap:run": "npx cap run", "cap:build": "npm run build && npx cap sync", "build:protected": "node scripts/build-protected.js", "license:create": "cd license-server && node scripts/create-license.js", "license:server": "cd license-server && npm start"}, "dependencies": {"@capacitor/android": "^5.5.1", "@capacitor/app": "^5.0.6", "@capacitor/browser": "^5.2.1", "@capacitor/camera": "^5.0.10", "@capacitor/clipboard": "^5.0.8", "@capacitor/core": "^5.5.1", "@capacitor/device": "^5.0.8", "@capacitor/filesystem": "^5.2.2", "@capacitor/geolocation": "^5.0.6", "@capacitor/haptics": "^5.0.6", "@capacitor/ios": "^5.5.1", "@capacitor/keyboard": "^5.0.9", "@capacitor/local-notifications": "^5.0.8", "@capacitor/network": "^5.0.6", "@capacitor/push-notifications": "^5.1.0", "@capacitor/screen-orientation": "^5.0.8", "@capacitor/share": "^5.0.8", "@capacitor/status-bar": "^5.0.6", "@capacitor/toast": "^5.0.8", "@heroicons/vue": "^2.2.0", "@netlify/functions": "^2.4.0", "@prisma/client": "^6.8.2", "@types/crypto-js": "^4.2.2", "@vueuse/core": "^10.5.0", "axios": "^1.6.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "node-fetch": "^3.3.2", "nodemailer": "^6.9.7", "pinia": "^2.1.7", "prisma": "^6.8.2", "serverless-http": "^3.2.0", "vue": "^3.3.8", "vue-i18n": "^9.14.4", "vue-router": "^4.2.5"}, "devDependencies": {"@capacitor/cli": "^5.5.1", "@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.18.5", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.16", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.31", "prettier": "^3.0.3", "tailwindcss": "^3.3.5", "typescript": "~5.2.0", "vite": "^4.4.11", "vite-plugin-pwa": "^0.17.0", "vue-tsc": "^1.8.19", "workbox-window": "^7.0.0"}}